# ATMA Backend End-to-End Load Test

Program load testing untuk menguji kekuatan ekosistem ATMA Backend dengan simulasi 100 user concurrent yang melakukan complete user journey dari register sampai mendapat hasil assessment yang tersimpan di database.

## 🎯 Tujuan

Memastikan sistem ATMA Backend dapat menangani **100 user concurrent** dengan:
- ✅ Register user baru
- ✅ Login user  
- ✅ Submit assessment data
- ✅ Proses AI analysis (menggunakan mock AI untuk testing)
- ✅ Simpan hasil ke database
- ✅ Retrieve hasil dari database

## 🚀 Quick Start

### 1. Quick Validation Test (Recommended First)
```bash
# Test kecil untuk validasi sistem (5 users)
quick-test.bat
```

### 2. Setup Load Test
```bash
# Jalankan setup script dengan validation
setup-load-test.bat
```

### 3. Start ATMA Services
```bash
# Start semua services ATMA
start-all-simple.bat
```

### 4. Run Load Test
```bash
# Jalankan load test dengan UI
run-load-test.bat

# Atau manual:
node load-test-e2e.js 100 1500
```

## 📊 Test Scenarios

### Pre-configured Tests

| Test Type | Users | Delay | Description |
|-----------|-------|-------|-------------|
| **Quick** | 5 | 1000ms | Quick validation test |
| **Small** | 10 | 500ms | Light test untuk validasi awal |
| **Medium** | 50 | 1000ms | Moderate load test |
| **Large** | 100 | 1500ms | **TARGET TEST** - Heavy load |
| **Stress** | 200 | 2000ms | Stress test untuk batas maksimal |

### Custom Test
```bash
node load-test-e2e.js [concurrent_users] [delay_between_batches]

# Contoh:
node load-test-e2e.js 100 1000  # 100 users, 1s delay between batches

# Quick validation:
quick-test.bat                  # 5 users quick test
```

## 🔧 Configuration

Edit `CONFIG` object di `load-test-e2e.js`:

```javascript
const CONFIG = {
  API_BASE_URL: 'http://localhost:3000',
  CONCURRENT_USERS: 100,           // Jumlah user concurrent
  DELAY_BETWEEN_BATCHES: 1500,     // Delay antar batch (ms)
  BATCH_SIZE: 5,                   // User per batch
  MAX_WAIT_TIME: 300000,           // Max wait untuk processing (5 menit)
  POLL_INTERVAL: 5000,             // Interval check hasil (5 detik)
  USE_MOCK_AI: true                // Gunakan mock AI (recommended)
};
```

## 📈 Metrics yang Diukur

### Success Metrics
- **Success Rate**: Persentase user yang berhasil complete journey
- **Throughput**: User per second yang berhasil diproses
- **Registration Success**: Persentase registrasi berhasil
- **Login Success**: Persentase login berhasil  
- **Submission Success**: Persentase assessment submission berhasil
- **Processing Success**: Persentase AI processing berhasil

### Performance Metrics
- **Response Times**: Min, Max, Average, P95 untuk setiap step
- **End-to-End Time**: Total waktu dari register sampai hasil tersimpan
- **Processing Time**: Waktu AI analysis dan penyimpanan ke database

### Error Tracking
- Registration errors
- Login errors
- Submission errors
- Processing errors
- Timeout errors

## 📋 Sample Output

```
🏁 LOAD TEST RESULTS
================================================================================

📊 OVERVIEW:
   Total Users: 100
   Success Rate: 95.0% (95/100)
   Total Time: 180s
   Throughput: 0.53 users/second

✅ SUCCESSFUL OPERATIONS:
   Registrations: 100/100 (100.0%)
   Logins: 100/100 (100.0%)
   Submissions: 98/100 (98.0%)
   Processing: 95/100 (95.0%)

❌ ERRORS:
   Registration: 0
   Login: 0
   Submission: 2
   Processing: 3
   Timeout: 0

⏱️  TIMING STATISTICS (ms):
   Registration - Avg: 245, Min: 180, Max: 450, P95: 380
   Login - Avg: 198, Min: 150, Max: 320, P95: 280
   Submission - Avg: 156, Min: 120, Max: 250, P95: 220
   Processing - Avg: 45000, Min: 30000, Max: 120000, P95: 85000
   End-to-End - Avg: 48500, Min: 35000, Max: 125000, P95: 90000

🟢 EXCELLENT: System handled the load very well!
```

## 🔍 Troubleshooting

### Services Not Running
```
❌ System health check failed: connect ECONNREFUSED 127.0.0.1:3000
💡 Make sure all services are running using: start-all-simple.bat
```
**Solution**: Jalankan `start-all-simple.bat` dan tunggu semua services start.

### Validation Errors
```
❌ User 1: Assessment submission failed - VALIDATION_ERROR
Validation failed: riasec.realistic must be an integer
```
**Solution**: Assessment data sudah diperbaiki untuk menggunakan integers. Jika masih error:
- Jalankan `node test-assessment-validation.js` untuk check data format
- Pastikan menggunakan versi terbaru dari `load-test-e2e.js`

### High Error Rate
```
🔴 POOR: System failed to handle the load, major issues need addressing.
```
**Solutions**:
- Kurangi jumlah concurrent users
- Increase delay between batches
- Check service logs untuk error details
- Increase worker capacity di `start-all-simple.bat`

### Timeout Errors
```
⏰ User 45: Timeout after 300s
```
**Solutions**:
- Increase `MAX_WAIT_TIME` di config
- Check analysis worker capacity
- Verify RabbitMQ is running properly

## 🎛️ System Requirements

### Minimum untuk 100 Users
- **RAM**: 8GB minimum, 16GB recommended
- **CPU**: 4 cores minimum, 8 cores recommended  
- **Storage**: SSD recommended untuk database performance
- **Network**: Stable connection untuk API calls

### Service Configuration
- **Analysis Workers**: 5 workers (default di start-all-simple.bat)
- **Worker Concurrency**: 5 jobs per worker
- **Total Capacity**: 25 concurrent analysis jobs
- **RabbitMQ**: Default configuration
- **Database**: PostgreSQL dengan connection pooling

## 🚨 Important Notes

1. **Use Mock AI**: Set `USE_MOCK_AI: true` untuk testing. Real Gemini API akan mahal dan lambat.

2. **Database Cleanup**: Load test akan create banyak test users. Clean up secara manual jika diperlukan.

3. **Rate Limiting**: API Gateway memiliki rate limiting. Adjust jika diperlukan untuk load testing.

4. **Resource Monitoring**: Monitor CPU, RAM, dan disk usage selama test.

5. **Gradual Testing**: Mulai dengan test kecil (10 users) sebelum full load test.

## 📞 Support

Jika ada issues dengan load test:
1. Check service logs di command windows
2. Verify semua services running dengan `netstat -an | findstr ":300"`
3. Check database connectivity
4. Monitor system resources

---

**Target**: Sistem harus dapat handle **100 concurrent users** dengan **success rate ≥ 90%** dan **average end-to-end time ≤ 60 seconds**.
