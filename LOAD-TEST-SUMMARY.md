# 🎯 ATMA Backend Load Test - Complete Package

Program load testing end-to-end yang telah dibuat untuk menguji kekuatan ekosistem ATMA Backend dengan simulasi **100 user concurrent**.

## 📁 Files yang Dibuat

### Core Load Test Files
- **`load-test-e2e.js`** - Main load testing script dengan complete user journey
- **`load-test-package.json`** - Dependencies untuk load test
- **`LOAD-TEST-README.md`** - Dokumentasi lengkap load testing

### Setup & Configuration Scripts
- **`setup-load-test.bat`** - Setup dependencies dan environment
- **`configure-mock-ai.bat`** - Configure Mock AI untuk testing cepat
- **`run-100-user-test.bat`** - All-in-one script untuk 100 user test

### Interactive Scripts
- **`run-load-test.bat`** - Interactive menu untuk berbagai test scenarios
- **`monitor-system.bat`** - Real-time system resource monitoring

### Utility Scripts
- **`cleanup-test-data.js`** - Cleanup test users dari database
- **`test-assessment-validation.js`** - Validate assessment data format
- **`quick-test.bat`** - Quick 5-user validation test

## 🚀 Quick Start Guide

### 1. Quick Validation (Recommended First)
```bash
# Quick 5-user test untuk validasi sistem
quick-test.bat
```

### 2. One-Click 100 User Test
```bash
# Jalankan complete 100 user test dengan setup otomatis
run-100-user-test.bat
```

### 3. Interactive Testing
```bash
# Menu interaktif untuk pilih test size
run-load-test.bat
```

### 4. Manual Testing
```bash
# Setup dependencies
setup-load-test.bat

# Configure mock AI
configure-mock-ai.bat

# Start services
start-all-simple.bat

# Run custom test
node load-test-e2e.js 100 1500
```

## 🎯 Test Scenarios

| Scenario | Command | Users | Description |
|----------|---------|-------|-------------|
| **Quick Validation** | `quick-test.bat` | 5 | Quick system validation |
| **Small Test** | `node load-test-e2e.js 10 500` | 10 | Light load test |
| **Medium Test** | `node load-test-e2e.js 50 1000` | 50 | Moderate load |
| **Target Test** | `node load-test-e2e.js 100 1500` | 100 | **Main target test** |
| **Stress Test** | `node load-test-e2e.js 200 2000` | 200 | Maximum stress |

## 📊 Complete User Journey

Setiap user akan melakukan:

1. **Register** - Create new user account
2. **Login** - Authenticate and get JWT token  
3. **Submit Assessment** - Send RIASEC, OCEAN, VIA-IS data
4. **Wait for Processing** - AI analysis via RabbitMQ queue
5. **Verify Results** - Check results saved in database

## 🔧 System Configuration

### Mock AI Setup
- **Mock AI Enabled**: Responses cepat tanpa API cost
- **Processing Time**: 1-3 detik per assessment
- **Realistic Data**: Dynamic assessment results based on input

### Load Test Configuration
```javascript
CONCURRENT_USERS: 100,        // Target concurrent users
BATCH_SIZE: 5,               // Users per batch
DELAY_BETWEEN_BATCHES: 1500, // 1.5s delay between batches
MAX_WAIT_TIME: 300000,       // 5 minutes max wait
POLL_INTERVAL: 5000,         // Check results every 5s
```

### System Capacity
- **Analysis Workers**: 5 workers
- **Worker Concurrency**: 5 jobs per worker  
- **Total Capacity**: 25 concurrent analysis jobs
- **Queue System**: RabbitMQ with retry logic

## 📈 Success Metrics

### Target Performance
- **Success Rate**: ≥ 90% complete journeys
- **End-to-End Time**: ≤ 60 seconds average
- **Throughput**: ≥ 1 user/second processing
- **Error Rate**: ≤ 10% total errors

### Measured Metrics
- ✅ Registration success rate
- ✅ Login success rate  
- ✅ Assessment submission success rate
- ✅ AI processing success rate
- ✅ Database save success rate
- ⏱️ Response times for each step
- ⏱️ End-to-end completion time
- 📊 System resource usage

## 🔍 Monitoring & Debugging

### Real-time Monitoring
```bash
# Monitor system resources during test
monitor-system.bat
```

### Log Analysis
- **API Gateway**: Check proxy logs
- **Assessment Service**: Check submission logs
- **Analysis Workers**: Check processing logs  
- **Archive Service**: Check database save logs

### Common Issues & Solutions

#### High Error Rate
```
🔴 POOR: System failed to handle the load
```
**Solutions**:
- Increase worker count in `start-all-simple.bat`
- Reduce concurrent users
- Check database connection pool
- Monitor system resources

#### Timeout Errors
```
⏰ User 45: Timeout after 300s
```
**Solutions**:
- Increase `MAX_WAIT_TIME`
- Check RabbitMQ queue status
- Verify worker capacity
- Check database performance

#### Service Unavailable
```
❌ API Gateway (port 3000) is not running
```
**Solutions**:
- Run `start-all-simple.bat`
- Check port conflicts
- Verify all services started
- Check system resources

## 🧹 Cleanup & Maintenance

### Cleanup Test Data
```bash
# Remove all test users from database
node cleanup-test-data.js
```

### Restore Original Configuration
```bash
# Restore original AI configuration
copy analysis-worker\.env.backup analysis-worker\.env
```

### Reset System
```bash
# Stop all services
taskkill /f /im node.exe

# Restart services
start-all-simple.bat
```

## 📋 Pre-Test Checklist

- [ ] All ATMA services running (`start-all-simple.bat`)
- [ ] Mock AI configured (`configure-mock-ai.bat`)
- [ ] Dependencies installed (`setup-load-test.bat`)
- [ ] System resources available (8GB+ RAM, 4+ CPU cores)
- [ ] Database accessible and clean
- [ ] RabbitMQ running and accessible

## 🎯 Expected Results for 100 Users

### Excellent Performance (Target)
```
Success Rate: 95%+ 
End-to-End Avg: 45-60s
Throughput: 1.5+ users/second
Registration: <500ms avg
Login: <300ms avg  
Submission: <200ms avg
Processing: 30-60s avg
```

### Acceptable Performance
```
Success Rate: 85-94%
End-to-End Avg: 60-90s  
Throughput: 1.0+ users/second
Some timeout errors acceptable
```

### Poor Performance (Needs Optimization)
```
Success Rate: <85%
End-to-End Avg: >90s
High error rates
System resource exhaustion
```

## 🚨 Important Notes

1. **Database Growth**: 100 users akan create ~100 user records + assessment results
2. **Resource Usage**: Monitor CPU, RAM, dan disk I/O selama test
3. **Network Load**: Significant HTTP traffic ke semua services
4. **Queue Load**: RabbitMQ akan handle 100 concurrent messages
5. **Mock AI**: Pastikan `USE_MOCK_MODEL=true` untuk testing

## 📞 Troubleshooting Support

Jika ada issues:
1. Check `LOAD-TEST-README.md` untuk detailed troubleshooting
2. Monitor system resources dengan `monitor-system.bat`
3. Check individual service logs di command windows
4. Verify database connectivity dan performance
5. Check RabbitMQ management interface (jika available)

---

**🎯 Goal**: Membuktikan sistem ATMA Backend dapat handle **100 concurrent users** dengan **success rate ≥ 90%** dan **performance yang acceptable** untuk production deployment.
