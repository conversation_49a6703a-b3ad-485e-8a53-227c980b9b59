{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:07:57","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:07:57","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:07:57","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:09:36","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:09:36","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:09:36","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:10:10","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:10:10","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:10:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:13:05","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:13:05","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:13:05","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 10:13:05","version":"1.0.0"}
{"concurrency":5,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:13:05","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 10:13:05","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:15:43","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:15:43","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:15:43","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 10:15:43","version":"1.0.0"}
{"concurrency":5,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:15:43","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 10:15:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:16:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:16:43","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:17:13","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:17:43","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:18:10","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:18:10","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:18:10","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 10:18:10","version":"1.0.0"}
{"concurrency":5,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:18:10","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 10:18:10","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:19:10","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:19:10","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:19:10","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 10:19:10","version":"1.0.0"}
{"concurrency":5,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:19:10","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 10:19:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:19:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:20:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:20:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:21:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:21:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:22:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:22:40","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:23:10","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:23:40","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:25:20","version":"1.0.0","workerConcurrency":"5"}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:25:20","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:25:20","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 10:25:20","version":"1.0.0"}
{"concurrency":5,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:25:20","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 10:25:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:25:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:26:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:26:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:27:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:27:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:28:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:28:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:29:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:29:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:30:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:30:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:31:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:31:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:32:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:32:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:33:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:33:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:34:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:34:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:35:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:35:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:36:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:36:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:37:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:37:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:38:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:38:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:39:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:39:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:40:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:40:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:41:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:41:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:42:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:42:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:43:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:43:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:44:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:44:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:45:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:45:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:46:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:46:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:47:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:47:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:48:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:48:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:49:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:49:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:50:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:50:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:51:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:51:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:52:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:52:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:53:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:53:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:54:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:54:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:55:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:55:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:56:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:56:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:57:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:57:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:58:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:58:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:59:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 10:59:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:00:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:00:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:01:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:01:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:02:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:02:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:03:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:03:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:04:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:04:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:05:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:05:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:06:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:06:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:07:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:07:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:08:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:08:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:09:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:09:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:10:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:10:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:11:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:11:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:12:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:12:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:13:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:13:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:14:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:14:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:15:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:15:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:16:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:16:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:17:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:17:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:18:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:18:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:19:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:19:50","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:20:20","version":"1.0.0"}
{"level":"info","message":"Worker heartbeat","service":"analysis-worker","status":"running","timestamp":"2025-07-18 11:20:50","version":"1.0.0"}
