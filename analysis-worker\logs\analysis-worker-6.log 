{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:08:03","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:08:03","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:08:03","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:09:42","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:09:42","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:09:42","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:10:16","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:10:16","url":"amqp://localhost:5672","version":"1.0.0"}
{"error":"Channel closed by server: 406 (PRECONDITION-FAILED) with message \"PRECONDITION_FAILED - inequivalent arg 'durable' for exchange 'atma_exchange' in vhost '/': received 'false' but current is 'true'\"","level":"error","message":"RabbitMQ connection error","service":"analysis-worker","timestamp":"2025-07-18 10:10:16","version":"1.0.0"}
{"environment":"development","level":"info","message":"Analysis Worker starting up","queueName":"assessment_analysis","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0","workerConcurrency":"5 "}
{"level":"info","message":"Connecting to RabbitMQ...","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","url":"amqp://localhost:5672","version":"1.0.0"}
{"deadLetterQueue":"assessment_analysis_dlq","exchange":"atma_exchange","level":"info","message":"RabbitMQ connection established successfully","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
{"level":"info","message":"Queue consumer initialized successfully","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
{"concurrency":5,"exchange":"atma_exchange","level":"info","message":"Started consuming messages from queue","queue":"assessment_analysis","routingKey":"analysis.process","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
{"level":"info","message":"Analysis Worker started successfully and is consuming messages","service":"analysis-worker","timestamp":"2025-07-18 10:13:11","version":"1.0.0"}
