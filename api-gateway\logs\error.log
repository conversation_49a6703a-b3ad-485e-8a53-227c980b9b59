{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:08:57","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:08:57","url":"/unknown-route"}
{"contentLength":"599","duration":"54ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:10:24","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:10:24","url":"/unknown-route"}
{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:15:47","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:15:47","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:20:51","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:20:51","url":"/unknown-route"}
{"contentLength":"599","duration":"82ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:22:04","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:22:04","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:25:52","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:25:52","url":"/unknown-route"}
{"contentLength":"85","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 17:31:16","url":"/auth/"}
{"contentLength":"604","duration":"52ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:31:26","url":"/health"}
{"contentLength":"606","duration":"136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:32:46","url":"/health"}
{"contentLength":"605","duration":"35ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:26","url":"/health"}
{"contentLength":"1470","duration":"131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"contentLength":"238","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"contentLength":"606","duration":"254ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:28","url":"/health"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"contentLength":"114","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:29","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:05"}
{"contentLength":"118","duration":"3955ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:05","url":"/auth/register"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:09"}
{"contentLength":"118","duration":"3940ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:09","url":"/auth/login"}
{"contentLength":"650","duration":"53ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 22:31:59","url":"/health"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 22:32:32","url":"/auth/health"}
{"contentLength":"86","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/archive/health"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"108","duration":"42ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-15 22:32:33","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:19","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/archive/health"}
{"contentLength":"375","duration":"23ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:19","url":"/auth/register"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:41","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/assessments/health"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/archive/health"}
{"contentLength":"375","duration":"15ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:41","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:08:43"}
{"contentLength":"114","duration":"2547ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:08:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:10:34","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/archive/health"}
{"contentLength":"375","duration":"21ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:10:34","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:10:37"}
{"contentLength":"114","duration":"2543ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:10:37","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:11:17","url":"/auth/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/archive/health"}
{"contentLength":"375","duration":"20ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:11:17","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:11:20"}
{"contentLength":"114","duration":"2535ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:11:20","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:02","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/archive/health"}
{"contentLength":"375","duration":"17ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:02","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"49ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:51","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/archive/health"}
{"contentLength":"375","duration":"16ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:51","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"52ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:14:45","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/archive/health"}
{"contentLength":"375","duration":"51ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:14:45","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"57ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:16:17","url":"/auth/health"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/archive/health"}
{"contentLength":"375","duration":"47ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:16:17","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:20:58","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/archive/health"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:20:58","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:27:52","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/archive/health"}
{"contentLength":"375","duration":"31ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:27:52","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:32:48","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/archive/health"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:49","url":"/assessments/submit"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:34:09","url":"/auth/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/archive/health"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:34:09","url":"/auth/register"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/submit"}
{"contentLength":"599","duration":"79ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 08:16:14","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-16 08:16:14","url":"/unknown-route"}
{"contentLength":"599","duration":"72ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 09:51:46","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-16 09:51:46","url":"/unknown-route"}
{"contentLength":"120","duration":"32ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:26:33","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"10ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:26:33","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"12ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:28:54","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"7ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:28:54","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:46"}
{"contentLength":"111","duration":"13ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:46","url":"/archive/results?page=1&limit=10","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:46"}
{"contentLength":"111","duration":"11ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:46","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:46"}
{"contentLength":"111","duration":"7ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:47","url":"/archive/results?page=1&limit=10","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:47"}
{"contentLength":"111","duration":"5ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:47","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 13:55:54","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"2006","duration":"428ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 13:55:55","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"44ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:42:04","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:42:04","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:00","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"8ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:00","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"11ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:11","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"19ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:11","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"27ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:44","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"7ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:44","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"8ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:57","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"11ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:57","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 15:35:15","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"2006","duration":"232ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 15:35:15","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"548","duration":"58ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 15:58:15","url":"/auth/register"}
{"contentLength":"564","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:17","url":"/auth/login"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:33","url":"/auth/profile"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"PUT","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:36","url":"/auth/profile"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:39","url":"/auth/token-balance"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:42","url":"/assessments/submit"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:45","url":"/archive/stats"}
{"contentLength":"564","duration":"36ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:50","url":"/auth/login"}
{"contentLength":"564","duration":"6ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:53","url":"/auth/login"}
{"contentLength":"564","duration":"53ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:18:32","url":"/auth/login"}
{"contentLength":"564","duration":"5ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:18:36","url":"/auth/login"}
{"contentLength":"564","duration":"40ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:18:49","url":"/auth/login"}
{"contentLength":"564","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:24:43","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:25:26","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"contentLength":"1672","duration":"15ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 16:25:26","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:28:13","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"contentLength":"1672","duration":"14ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 16:28:13","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:30:44","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:33:55","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 18:58:48","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"contentLength":"564","duration":"11ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-17 06:00:23","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 08:01:55","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 10:47:33","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"contentLength":"246","duration":"45ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-17 10:47:33","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 10:49:39","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"contentLength":"246","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-17 10:49:39","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 12:13:16","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 12:22:48","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-17 12:28:00"}
{"contentLength":"114","duration":"60ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-17 12:28:00","url":"/assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 12:31:22","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/auth/profile"}
{"contentLength":"128","duration":"7ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/auth/profile"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/stats"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:17","url":"/auth/logout"}
{"contentLength":"128","duration":"4ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:18","url":"/auth/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:23","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 18:23:11","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 18:26:39","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 19:31:29","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 19:34:32","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:27:31","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:28:33","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"contentLength":"128","duration":"6ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:03","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:04","url":"/archive/results/1566a067-09e2-4973-a382-c8dc4a973299"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:04","url":"/archive/results/1566a067-09e2-4973-a382-c8dc4a973299"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:06","url":"/auth/token-balance"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:06","url":"/auth/token-balance"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/auth/profile"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:09","url":"/auth/token-balance"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:09","url":"/auth/token-balance"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/auth/profile"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/auth/profile"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:33:46","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-17 20:36:28","url":"/assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:38:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:46:59","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:58:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 21:06:48","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 21:07:18","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/auth/profile"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:57","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:57","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:57","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:08:02","url":"/auth/logout"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:08:03","url":"/auth/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:08:18","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 03:53:50","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 03:55:38","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 03:56:19","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Admin auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:30:23"}
{"contentLength":"124","duration":"26ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:30:23","url":"/admin/login"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Admin auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:30:44"}
{"contentLength":"124","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:30:44","url":"/admin/login"}
{"contentLength":"1867","duration":"50ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:35:39","url":"/admin/login"}
{"contentLength":"1867","duration":"31ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:36:04","url":"/admin/login"}
{"contentLength":"505","duration":"343ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:39:59","url":"/admin/login"}
{"code":"ERR_HTTP_HEADERS_SENT","error":"Cannot set headers after they are sent to the client","level":"error","message":"Admin auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:47:11"}
{"contentLength":"124","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:47:11","url":"/admin/login"}
{"contentLength":"505","duration":"324ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:49:10","url":"/admin/login"}
{"contentLength":"505","duration":"303ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:53:45","url":"/admin/login"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:13"}
{"contentLength":"117","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:13","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:13"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:13","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:28"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:28","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:28"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:28","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:32"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:32","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:32"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:32","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:41"}
{"contentLength":"117","duration":"3ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:41","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:41"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:41","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:43"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:43","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:43"}
{"contentLength":"117","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:43","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:45"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:45","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:45"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:45","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:49"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:49","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:49"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:49","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:58:52"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:58:52","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:59:27"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:59:27","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"contentLength":"82","duration":"6ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/profile"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/profile"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/profile"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/profile"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"83","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:18:04","url":"/admin/profile"}
{"contentLength":"83","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:18:04","url":"/admin/profile"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/profile"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/logout"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/logout"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:34:17","url":"/admin/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:34:20","url":"/admin/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:36:42","url":"/admin/login"}
{"contentLength":"82","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 07:03:43","url":"/admin/users?page=1&limit=5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:20:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:29:40","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:34:16","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:40:10","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:21:48","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:22:12","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:23:30","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:24:00","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:32:37","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:38:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:52:55","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:55:30","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:58:33","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:14","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/auth/profile"}
{"contentLength":"480","duration":"374ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 09:10:06","url":"/auth/login"}
{"contentLength":"548","duration":"49ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 09:10:31","url":"/auth/register"}
{"contentLength":"480","duration":"280ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 09:10:31","url":"/auth/login"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30060ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30062ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30062ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30064ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30065ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30067ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30068ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30070ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30071ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30073ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30073ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:42:08"}
{"contentLength":"118","duration":"30075ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:42:08","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:21","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:26","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"contentLength":"161","duration":"7060ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"contentLength":"161","duration":"7057ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"contentLength":"161","duration":"7057ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"contentLength":"161","duration":"7051ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"contentLength":"161","duration":"7040ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"contentLength":"161","duration":"7047ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"contentLength":"161","duration":"7046ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"contentLength":"161","duration":"7047ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"contentLength":"161","duration":"7036ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"contentLength":"161","duration":"7034ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"contentLength":"161","duration":"7148ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"contentLength":"161","duration":"7147ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"contentLength":"161","duration":"7146ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"contentLength":"161","duration":"7144ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"contentLength":"161","duration":"7141ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"contentLength":"161","duration":"7135ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"contentLength":"161","duration":"7132ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"contentLength":"161","duration":"7130ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"contentLength":"161","duration":"7128ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"contentLength":"161","duration":"7128ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"contentLength":"161","duration":"7129ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"contentLength":"161","duration":"7130ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:42:28","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"contentLength":"161","duration":"7168ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"contentLength":"161","duration":"7169ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"contentLength":"161","duration":"2084ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"contentLength":"161","duration":"2080ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"contentLength":"161","duration":"2076ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"contentLength":"161","duration":"2066ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"contentLength":"161","duration":"2063ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"contentLength":"161","duration":"2049ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"contentLength":"161","duration":"2047ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"contentLength":"161","duration":"2044ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"contentLength":"161","duration":"2042ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"contentLength":"161","duration":"2041ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"contentLength":"161","duration":"2020ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"contentLength":"161","duration":"2018ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"contentLength":"161","duration":"2018ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"contentLength":"161","duration":"2019ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"contentLength":"161","duration":"2019ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"contentLength":"161","duration":"2018ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"contentLength":"161","duration":"2017ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"contentLength":"161","duration":"1996ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"contentLength":"161","duration":"1993ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"contentLength":"161","duration":"1991ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"contentLength":"161","duration":"1990ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"contentLength":"161","duration":"1990ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"contentLength":"161","duration":"1989ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"contentLength":"161","duration":"1988ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"contentLength":"161","duration":"1989ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"contentLength":"161","duration":"1988ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"contentLength":"161","duration":"1983ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"contentLength":"161","duration":"1980ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"contentLength":"161","duration":"1979ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"contentLength":"161","duration":"1971ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"contentLength":"161","duration":"1964ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"contentLength":"161","duration":"1995ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"contentLength":"161","duration":"259ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"contentLength":"161","duration":"245ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"contentLength":"161","duration":"1976ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"contentLength":"161","duration":"1974ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"contentLength":"161","duration":"1973ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"contentLength":"161","duration":"244ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"contentLength":"161","duration":"228ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"contentLength":"161","duration":"225ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"contentLength":"161","duration":"224ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"contentLength":"161","duration":"218ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"contentLength":"161","duration":"216ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"contentLength":"161","duration":"215ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"contentLength":"161","duration":"213ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"contentLength":"161","duration":"213ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"contentLength":"161","duration":"211ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"contentLength":"161","duration":"208ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"contentLength":"161","duration":"207ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"contentLength":"161","duration":"209ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"contentLength":"161","duration":"206ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"contentLength":"161","duration":"204ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"contentLength":"161","duration":"202ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"contentLength":"161","duration":"202ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"contentLength":"161","duration":"199ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"contentLength":"161","duration":"198ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"contentLength":"161","duration":"168ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"contentLength":"161","duration":"163ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"contentLength":"161","duration":"160ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"contentLength":"161","duration":"155ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"contentLength":"161","duration":"151ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"contentLength":"161","duration":"149ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"contentLength":"161","duration":"175ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"contentLength":"161","duration":"164ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:42:28","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"contentLength":"90","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-18 12:43:13","url":"/api/health"}
{"contentLength":"548","duration":"74ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"72ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"73ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"73ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"74ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"76ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"76ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"76ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"86ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"87ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:29","url":"/auth/register"}
{"contentLength":"548","duration":"36ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:43:59","url":"/auth/register"}
{"contentLength":"480","duration":"272ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:44:05","url":"/auth/login"}
{"contentLength":"548","duration":"57ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:29","url":"/auth/register"}
{"contentLength":"548","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"21ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"22ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"22ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"22ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"25ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"32ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"37ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"43ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"48ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:44:42","url":"/auth/register"}
{"contentLength":"548","duration":"37ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:45:01","url":"/auth/register"}
{"contentLength":"548","duration":"44ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:45:24","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:45:25","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"contentLength":"161","duration":"14ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:45:25","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"contentLength":"548","duration":"38ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:45","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:46","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"contentLength":"548","duration":"21ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"22ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"23ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"25ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"27ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"58ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"63ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"70ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"72ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"contentLength":"548","duration":"74ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:46:53","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:55","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:55","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:55","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:55","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:56","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:56","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:56","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:56","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:56","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:46:56","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"contentLength":"548","duration":"19ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"21ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"21ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"21ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"contentLength":"548","duration":"21ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:02","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:05","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"contentLength":"548","duration":"265ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"262ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"261ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"263ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"263ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"260ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"261ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"263ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"263ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"263ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"278ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"278ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"279ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"279ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"269ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"269ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"269ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"269ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"269ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"269ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"269ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"271ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"271ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"272ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"274ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"279ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"282ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"282ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"283ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"283ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"283ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"284ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"286ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"286ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"288ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"293ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"295ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"296ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"297ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"296ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"297ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"305ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"310ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"312ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"312ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"312ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"312ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"311ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"313ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"314ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"322ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"329ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"329ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"327ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"328ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"328ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"328ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"328ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"328ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"328ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"327ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"327ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"328ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"329ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"348ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"348ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"348ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"348ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"348ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"346ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"349ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"349ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"343ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"342ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"342ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"343ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"343ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"341ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"341ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"340ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"340ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"340ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"339ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"335ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"334ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"334ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"334ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"335ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"334ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"333ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"311ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"311ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"310ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"310ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"311ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"309ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"309ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"309ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"308ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"contentLength":"548","duration":"307ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:47:17","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:28","userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:36","userId":"bb269993-1022-43b2-8926-4fbf4043ba0c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:36","userId":"8cd96f02-f438-4e30-b81d-df35181b1dd3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:36","userId":"8f153039-6f2b-411e-af79-c636d53cc3f9"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:36","userId":"bc754f4a-5ac0-4053-8bba-a2a42a31a746"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:36","userId":"80b44903-76e5-4d53-9831-574ecab7f147"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:36","userId":"cb7a1876-d50d-4ac0-b263-6790e313d4f8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:36","userId":"392fd33d-ab74-4da6-a91a-02c56656a57c"}
{"contentLength":"96","duration":"10036ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"contentLength":"96","duration":"10046ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b41051d1-550d-4d43-a899-ff6731e904ef"}
{"contentLength":"96","duration":"10046ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"d9acc7c4-6671-4254-8f39-6034e3924c8b"}
{"contentLength":"96","duration":"10046ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"15902e84-9592-4b67-b3c2-1ec79e1dd54d"}
{"contentLength":"96","duration":"10046ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"a95c638e-a5c5-4d5a-bac3-440783832e88"}
{"contentLength":"96","duration":"10040ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"6c14d02a-a968-495c-889c-d9a3398c74a0"}
{"contentLength":"96","duration":"10040ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"2d1c9020-140a-46dc-842d-2ca54be7a854"}
{"contentLength":"96","duration":"10041ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"842827f5-e6a0-464c-adee-d849b9ffb009"}
{"contentLength":"96","duration":"10031ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ff453c60-d4cc-4a3b-895b-58aea0d76994"}
{"contentLength":"96","duration":"10031ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"078de71f-122d-41ec-be55-b4b93c20d6b8"}
{"contentLength":"96","duration":"10031ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b74f8249-1c2d-4db6-ad8d-6af943390e77"}
{"contentLength":"96","duration":"10030ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"0ee6c0c4-05ae-4a76-8872-6bc82a9413df"}
{"contentLength":"96","duration":"10027ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"43411672-8178-4b77-a062-73d1340d6dd3"}
{"contentLength":"96","duration":"10027ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee52423e-4703-4517-8fe5-b8661b377999"}
{"contentLength":"96","duration":"10027ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8d16eed0-635c-4881-900f-1fdfcee24078"}
{"contentLength":"96","duration":"10027ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"384e4e16-5ef1-4fc2-93c0-d0b08e952d12"}
{"contentLength":"96","duration":"10026ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 12:47:38","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b48bb1d1-7bf9-4262-8655-88c3c751a97e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"849ecb09-081f-43d5-a71a-6bca85f6a8aa"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"6bc0b5c1-151e-4b05-b743-4762063acbee"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"009e09fc-311a-4851-949e-b76859139743"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"040790f4-eeb4-46d7-84e7-768e0f8c500b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"4aa44128-345e-4ebe-b3eb-39676d2596ce"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"12e2ba85-6226-4701-80f5-f7831eedfd48"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"d6d1f30e-be74-494c-8a9b-bb501e91a0e3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"b29a61ec-cda2-4ce1-9e63-a655109ba149"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"b03799da-c6da-4925-87c6-901060f1ffc3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"3dbea5fc-4925-4aa7-bfcd-318599a9e5b1"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"8f72b4c2-3265-4ba0-8f52-6a1ff9d5e497"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"7e1bb330-f26a-4a0f-a0df-b16e75e63d36"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"c52e498b-c989-4e27-b783-2fe3316085a3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"3653691f-57cf-4782-8edf-157e46f19bd8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"11556c6b-f185-42ce-a3b6-369dd56a6ac5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"d7d7720a-b095-4f24-8dea-4b4fae817a5a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"68abe24d-9fd9-4a34-9538-81ebed775b4c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"ed022609-ebc6-41d0-a464-2dc10ca8773e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"45f1e061-9c35-4aad-802e-6d1a58fbdaf0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"f1b2443e-1fd7-40a2-b26e-001251819505"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"24a91af5-f630-4d85-8a86-1863e3c977ec"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"c540dde8-d7e7-4b31-946b-c44073078958"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"c760022b-5504-4436-b0df-12e523fb08cf"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"950a1c16-ba41-4ac6-bea8-b052c6bd32fb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"b713b871-2151-4908-82c7-e25043087388"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"31ffaa30-7eca-4caa-a144-e4d4184797f8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"27046cf8-947c-460f-bcac-21f0770d22ec"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"15060bb5-db18-49b3-931e-9222c270e677"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"2280d0b1-52dd-4ea1-af58-caf1a0dc1ccb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"8644861e-dabc-4b8b-a627-a0ae38bc44fe"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"36a5194c-32f2-4426-b5e4-76f19f18b956"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"b7572a8f-d3ba-410d-9871-a3439187285c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"cc61ca36-c057-44e6-8dee-f275f24dc1c0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"49862644-6bd8-4161-bbf0-e0bb0565df48"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"f47f861d-74bb-4198-a24d-8509841f9e80"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"072f69fa-6cbe-400b-b796-7e3271da936d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"fd129d2c-39d3-404c-a103-21a379463fcb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"b4bc5f99-ad7c-4687-83c5-9e9e21aeba64"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"9b4c5ed1-e673-4ee9-b696-5be44a561be0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:42","userId":"a26a6dd4-23e9-46f7-b782-15f7859d8342"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"8c044fe4-4f97-4ace-8360-641d1fe1e52c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"778981a0-fa31-4a3a-9d23-e9cbb9c0c952"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"37571634-9fe7-47c1-b50d-a0b5c5918171"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"5cc55727-a792-495d-ac51-0d0e844e5d53"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"2db8eac1-765e-4011-bb8d-7b92904ac01b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"68b8a7f4-00cf-4787-9396-f1983ed53648"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"727601f0-6746-4c23-b6af-d4d8f27c36b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"6761c652-3586-4274-8e32-44b45eb0ab1b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"f9b12420-847f-4968-b8b9-4153fdc8aae2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:44","userId":"4f114070-2569-44d2-ae17-e0e84d825a51"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"c50c6b3a-274f-47ad-97e4-19a762a6aed6"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"44bf17ee-fb4d-4a0a-ab8c-ea5ac863032d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"25a10f5b-94bb-4e95-b143-09ead677e663"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"4b9c8855-018c-44b3-89ef-8f0201454847"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"39ede2d4-b490-4c9a-b193-f3328fbd4857"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"ce779b7f-d252-445b-99cf-776c8d253223"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"9ae3d55e-01fd-4000-9698-d17c23b6e39c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"8eaf3b43-8867-4573-91ec-926d03d93a07"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"77a4b43c-1906-4c6e-ab4b-7cf5ca9cc4fe"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"da9eee0f-2f64-4776-943c-45d0496b69e0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"69ae9167-80c7-4164-8f08-14fc7e5f96b8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"3de2f9e7-75b6-429a-90a4-b4366adf0874"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"ae217bd8-d2f3-4a8b-aac3-b3516790c2e2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"829d69b3-2fdf-44eb-86a6-1d04b90cc581"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"7f361f12-3132-4693-8a61-f2ad8a8c6b0c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"1beefbcc-ded3-4675-85df-6c1468d16c02"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"e5084c4d-a410-4bed-aaae-8b1e791b1180"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"50520da3-8855-4d53-bac8-711fc04cfd19"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"c0a5909b-84fd-4460-8b7d-76601a8a73e2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"ee3c2f77-9ce0-4e25-a239-0ccdb4b80c24"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"63a9574c-b28b-4dc2-b0a6-0e009a92c1ae"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"62a862d9-1280-4b90-b469-db2e46dd6941"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"0d81b437-2a5b-45bb-87e3-b22638c79d2d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"4bcc4e0f-367a-4ecd-a233-ec3e9d48de38"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"7dde7770-1e0e-481e-af32-84bc96240188"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:47:45","userId":"c48a95d5-717b-487b-b2ce-61d7e09e5b43"}
{"contentLength":"548","duration":"112ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 12:52:43","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:52:44","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"contentLength":"161","duration":"58ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":402,"timestamp":"2025-07-18 12:52:44","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"da6e7756-6da7-4762-9478-15989c524ba5"}
{"contentLength":"597","duration":"31ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:56:11","url":"/health"}
{"contentLength":"611","duration":"64ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:57:24","url":"/health"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:33","userId":"cfa28d6d-330b-4cef-a724-b310132a0642"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:45","userId":"26448661-471a-4b23-8ab1-6679c27a27ef"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:45","userId":"c13ee8cc-7144-4ca1-b0b2-b9a909a89592"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:45","userId":"4ada4ec1-3775-4d55-b2d6-39f767814a67"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:45","userId":"1a3ec0f0-d95d-4f12-9671-71011df132f6"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:46","userId":"96fbbad0-3e6f-489c-816f-60f0f8ce2f02"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:46","userId":"f9d77206-0bfb-4fc4-a3ad-863fabbf194e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:46","userId":"d60630cd-340f-4697-811e-029a25930b18"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:46","userId":"1ed440a9-6d1e-4373-8a64-d953b395bc05"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:46","userId":"0d2d57e3-2fe3-440c-9fb1-ed3ed6805209"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:57:46","userId":"0dce7f0f-44ec-4609-a48f-c9dbdd4b55b6"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30038ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30040ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30042ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30044ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30047ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30048ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30051ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30053ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30055ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30058ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 12:58:28"}
{"contentLength":"118","duration":"30059ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 12:58:28","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"65d0aa1c-9be2-4e5a-a084-cee004367470"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"fde333bd-27bb-4d3e-913a-0990b71afbfb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"63d27f57-5494-40ba-9836-082f0b3404ed"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"c1fc0120-08ef-4332-9e93-70551c0ee0be"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"c406d275-f856-490c-88e7-1d65cabfbb79"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"f48235b1-0021-4b24-9e3f-b8252738e9ad"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"2dfa7646-1404-4763-bb5a-d2aab6786054"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"319fdabd-7d6c-4df0-b605-3026d21abc2d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"e1c9d4e2-9550-417a-a5d7-de160bbbc8b4"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"b4ab491f-ed6d-46d0-a9cd-f1b51719baab"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"f449e912-ce03-4387-80a6-ce4f71e77a30"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"747b5fde-04c1-47b4-bdd5-f5be41c1a3d2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"ade74031-dc44-4e25-9659-2311bdb2a4cc"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"35f2db47-b6ec-4ce6-a668-74453ae571e8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"c11cecd3-b465-4e57-8fb1-acfd4aca9b8d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"b6edae56-b8c3-45fb-95fb-08b5df506e72"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"db543ec8-2bfb-4cd9-9a0f-10ca575fb46f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"7055fdae-bb2a-4603-891b-16e9c93e4b01"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"69940f10-31e3-4323-8649-ddc0b821e825"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"26790b31-9604-43f7-ae37-16d8804f86f4"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"72dc1a2f-8b1b-4586-beac-c32cf8969848"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"0b1d7771-4b37-40d4-9b7f-706fae49370b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:41","userId":"1dd700fe-d27f-4109-b04f-7380e86c7d32"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"356e5538-f2a0-4f8c-9fa7-e1cb9e75171a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"8792fa51-c647-4f08-abd9-c49a716239f2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"d528fdd3-3235-483b-a43d-d1d1c29b01eb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"37d40867-0dda-4e5f-9ba7-b7548c6ea715"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"968fb035-bdfb-41a5-9734-69b83a1e85bf"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"2208cfb9-80ff-4d70-a625-e1d994a9093b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"5d7bd584-b48f-44f6-a5e8-d01808fd80cd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"6f19ce8a-9f83-456b-a11a-7513a10f0235"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"a6d5c5b2-9ace-4afe-bb24-91e007357a25"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"6ebd6622-507a-49c6-9c98-a061e87c1cb6"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"664c8c37-c9d3-4b84-97b3-2fe5214238ce"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"b73639d0-56f9-44f6-94c7-9cc3f7832f1a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"00ce7f85-1d83-412d-b6cd-9015bad9077c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"d0c8c8e4-4277-47cc-83d4-1548b6f20468"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"1cdb63e1-1d4c-4aa0-a685-ee21e6b9d236"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"db98a2d3-0cd6-49a6-8f57-f06a9bc6f458"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"0c34f35d-7a8b-4da6-95af-08ff4a700577"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"9072bb34-46f4-4208-a95c-56ef28f038e8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"444b3e73-8333-470a-882a-586df43fc9b0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"bd061e52-dee4-4e17-94f0-d403f2ea61c3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"ea223d63-4476-45d8-839f-31fbe4ed3194"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"dfd2e0d4-472e-4ccf-bec6-7e3a69d2725c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"91a792be-755a-4b66-a944-af43cf688f2f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"8e409b72-0a72-4f04-a9f4-79d81447a229"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"d4110d28-c32d-4cb6-9730-139ff41d1169"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"11464c13-8334-41e5-a97d-0098420f0173"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"7f9f4712-8f91-4c82-a890-120cb4c3ec1c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"f003da6f-4921-4f03-9241-071b0055fce6"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"6ec7837d-cc51-4f3b-8160-9c83e0bdbfe7"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"af03d9a7-6d00-46bf-b32d-ac507946342c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"60601ef6-c84c-4858-a78d-72ee46df3df4"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"a072bcbf-fbcd-4246-b68e-e39a8285eeb9"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"f8ab4a93-7814-4073-a5f1-df687584b743"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"d8c933fc-f1d8-4133-8aba-2c22549353ab"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"48623a90-e313-4c9a-a7a3-71ec5b4aff44"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"db281a40-84dc-4b26-86c8-bc8b06cd7aa7"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"0e88f689-a35b-4a21-b195-027b1b9e6681"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"1ae0bb96-cde0-42f9-be09-1aba1548cbdd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"719ef682-deef-4e94-ad96-9a1619a94a22"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"bacd761e-1249-4c5e-91ed-38ba0e1483bf"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:46","userId":"dcd7e23c-e9e2-4601-b286-c05f12ae152c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"0277d3c2-afe7-4b86-b7e0-103ecef32ec2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"092dad60-4419-4008-9705-a536ba6f2705"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"9fa89cbf-1f10-4657-b67e-206e296c2952"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"9d77de08-8bf3-44dc-b217-58a0080f0f35"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"69b239ec-3149-45f2-8302-a9dfb59966f2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"0e39e42d-d8b6-4ee3-a5c4-caf995b2d2e1"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"8495b445-c3f0-4f52-9423-ca88f55bc64c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"55cc0e87-ee68-4039-9769-4ae2749771d6"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"c8f5c2b2-9145-41cd-914a-c4686579469a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"eebfd093-9f16-4996-95b7-583c33ef843a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"ae657677-b65c-4ba4-9bfd-b75ad258b5f8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"79ab9798-02ba-402e-b76b-619f5a03ea9d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:47","userId":"b8328a76-1e84-488d-8dd6-a9d1986a2313"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"169b9c0e-3c0d-4357-89d2-db81430900ef"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"f046a9d0-febc-4b99-9f41-a1c094124720"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"fc10fd8a-740a-444e-942f-0db730c1509e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"53c8a423-e000-428a-b77b-21bf4261d6e7"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"c26ded89-4052-4dbf-abce-51259ebbc808"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"e4f3cf88-6cfe-4d63-871a-9c5ac041154f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"0bb56b1f-7fd7-4be9-bb6b-0e489815e19e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"8a8b0100-eb47-4534-b00a-58e1c7a9ad79"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"8d7bd5af-8ab1-4e88-bbe3-4093f504e1f8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"788fdba2-40df-4eba-8c20-b76e231fd57e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"36ae66c2-cf19-41b6-90a8-dfa780403177"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 12:58:48","userId":"ab81dc5c-dc57-4dc7-b57f-a8722ad50734"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30064ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30066ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30068ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30069ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30072ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30073ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30076ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30077ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30078ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30080ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30082ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30084ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30085ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30086ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:14:56"}
{"contentLength":"118","duration":"30089ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:14:56","url":"/auth/register"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"42a151cd-932e-43e8-b1fa-7c15eb45eff9"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"654c2a07-494c-43fe-a4ed-5ec7bafc778d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"f0e3dfc6-1083-4b40-b3aa-6fe282356c08"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"1dc379f5-cc22-43c9-9a25-0e0eb6c730eb"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"39ca051e-4c53-4686-a0b1-85320f3fb2e0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"e1be7515-a13b-4c08-98f5-dba73ce650a8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"1f3d1f4c-0316-4d37-9eca-ce8b750a90cd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"ea1ead65-23c9-4212-8bb3-9617e7bd0a71"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"79d56c37-2779-41c0-a9a6-69b0c8086781"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"fee432e2-9d44-4a97-9f9d-cd2094b01c2e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"1a51a3ea-26c9-42d6-a63e-d3e268d58d97"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"972d51f6-c872-41cb-a730-24e77a982b1e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"329a5988-739e-4274-a820-5d118c5fe329"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"baec6fa1-550f-4603-9468-b346d020c0e2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"f4ca6a0d-3a07-4d3c-8b42-4d94c8678657"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"fe1a3825-a09f-4542-beaf-e33cff34bf85"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"4cd3b055-2f63-484e-9cf0-28986f4b5a8a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"f14aa014-fdb2-4a69-8cec-c3fb10135436"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"fe724b99-218a-4ba1-896f-3948efc7fbc3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"ebf7b619-87fd-4a60-bc09-da658ce1d9e8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"5e695e9c-de2f-41d7-bca1-f1d71c016380"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"1d31137f-1b9b-459a-93d2-f0b06bc7e7c8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"dda0da85-f8a2-4bc7-8c72-074ab41504f1"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"46c973e1-ac61-46f8-99b5-da7a3ad1c05d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"88ccdeb0-005b-4304-9e68-4cc23e378e4e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"de1a9a4d-ec67-4802-8e0f-c1539b01e037"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:08","userId":"96ea4215-7e1d-4159-84ed-e05ec515b79b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"24a9ff0d-3b2b-4060-b363-e7dffcb9b80e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"ea5e54e4-b48f-48a5-b77a-e235846d2f83"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"51c3b889-bc04-4eb1-b373-95bfb4f8648c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"3d24f5db-0fdc-4fd5-82f1-1354431d9237"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"8fd9a6ce-eafe-472d-b32c-dac045be1a70"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"bf47a93a-bdd3-463f-a438-8284cf0fbe5c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"d44ed542-4f41-4751-a2f7-191b75ba08bd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"38aae3c7-2396-4e69-b685-81c92d992796"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"7e930078-4c34-45c5-b5c8-b819ea54333f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"88639b36-c5b0-478c-885e-9164e3700dd7"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"c4b2c1c0-3b30-420b-93bf-ac3524202eec"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"8c368d76-e437-4631-aadd-255f277a9c65"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:09","userId":"fa4d5550-3860-4f52-9477-1c4183924c93"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:36","userId":"176dde40-3718-4896-8e53-997a86509cb7"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:36","userId":"9aaedf10-d4ad-47e9-8239-82677b38a7db"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:37","userId":"02a66b2a-6672-41d7-8d09-c222eee529d9"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:37","userId":"888013b8-c6a4-4c0a-9974-bf30adace1f2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:37","userId":"05806676-8db8-45ae-a3e2-a0e1c0fff054"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:37","userId":"31ab1f93-f700-4d45-b1ff-ebb9d09c9999"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:38","userId":"5c23f129-01ea-4316-ab82-841cfcbd7e30"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:38","userId":"c7a19c95-8f59-4162-8bbb-1f78a1a46bcd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:38","userId":"a04b397a-b2c7-4ade-b310-07c2719cd2af"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:39","userId":"785629b1-9e1a-4e53-8031-4f1a6b95eadc"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:39","userId":"b62507f6-2b8b-4adb-9eaf-6fdf2ca80dc5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:39","userId":"12dd10ec-bb19-4b47-b6a6-5d50ed8a4b49"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:39","userId":"b4a7c97e-1a3d-4b36-a3da-596186a46128"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:39","userId":"cf5f12f2-749e-42f3-99bf-329da826c7aa"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:15:39","userId":"f2d5c8c0-75ed-4d5a-b1dd-d9bc9e55a1c9"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:17:47","userId":"c1270e87-2caa-45ff-8c61-0b6b581de56f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:17:47","userId":"3e69fa19-f07b-4bd5-9db5-5cffebce0d72"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:17:48","userId":"bcfc223e-169c-4c7e-9ab3-c7801c9a97a2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:17:48","userId":"3c3219bd-5206-4922-9c52-a60a04ba80e3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 13:17:48","userId":"dd95a27a-7b03-402f-a15f-2768ffffbc79"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8081ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8079ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8081ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8060ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8061ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8042ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8119ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8145ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8114ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8117ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8137ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8132ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8123ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8124ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8125ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8125ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8124ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8126ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8128ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8130ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8132ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8132ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8129ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8130ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8130ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8129ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8130ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8130ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8135ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8135ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8137ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8145ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8145ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8145ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8146ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8144ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8143ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8144ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8144ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8144ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8143ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8143ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8143ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8142ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8143ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8143ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8143ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8142ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8142ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8141ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8141ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8141ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8140ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8139ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8139ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8138ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8138ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8139ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8139ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8138ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8139ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8138ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8135ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8135ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8135ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8135ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8134ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8134ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8135ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8132ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8133ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8130ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8130ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8129ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8129ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8128ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8126ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8125ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8124ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8123ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8121ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNRESET","error":"read ECONNRESET","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:57:48"}
{"contentLength":"118","duration":"8120ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:57:48","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"26ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"25ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"25ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"24ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"25ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"24ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"23ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"22ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"22ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 13:58:00"}
{"contentLength":"118","duration":"22ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 13:58:00","url":"/auth/register"}
{"contentLength":"619","duration":"136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 14:39:38","url":"/health"}
{"contentLength":"619","duration":"62ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 14:39:55","url":"/health"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:30","userId":"59ead54d-1499-43ac-b1e2-21b301846323"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:30","userId":"b68f44a6-92bd-4479-aff8-71a6b5672d84"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:31","userId":"38953481-e219-4f53-837b-eae17b197933"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:31","userId":"7403908a-53ba-49b6-8bab-c6584d727224"}
{"contentLength":"1935","duration":"599ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:31","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"59ead54d-1499-43ac-b1e2-21b301846323"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:31","userId":"8c5ab1c0-8814-413c-87cf-51aeb8cf7ba9"}
{"contentLength":"1987","duration":"598ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:31","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b68f44a6-92bd-4479-aff8-71a6b5672d84"}
{"contentLength":"1987","duration":"31ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:31","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"38953481-e219-4f53-837b-eae17b197933"}
{"contentLength":"1923","duration":"28ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:31","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"7403908a-53ba-49b6-8bab-c6584d727224"}
{"contentLength":"1935","duration":"23ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:31","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8c5ab1c0-8814-413c-87cf-51aeb8cf7ba9"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:34","userId":"671d8a89-d625-419b-8d81-2dc0a464f86c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:35","userId":"eee31c0e-4932-42db-81f1-b7175cfd7455"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:35","userId":"953ea887-c89b-4137-8b68-a2691c0d4e62"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:35","userId":"f3b7f045-38fc-4a85-9e4e-fd55995872c6"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:35","userId":"993ae5dd-f586-4038-ac48-b0d00d8e9071"}
{"contentLength":"1987","duration":"473ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:35","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"671d8a89-d625-419b-8d81-2dc0a464f86c"}
{"contentLength":"1987","duration":"147ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:35","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"eee31c0e-4932-42db-81f1-b7175cfd7455"}
{"contentLength":"1935","duration":"143ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:35","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"953ea887-c89b-4137-8b68-a2691c0d4e62"}
{"contentLength":"1935","duration":"27ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:35","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"f3b7f045-38fc-4a85-9e4e-fd55995872c6"}
{"contentLength":"1987","duration":"24ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:35","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"993ae5dd-f586-4038-ac48-b0d00d8e9071"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:39","userId":"5f2a9eae-dda8-441c-aad1-a8be118ee405"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:39","userId":"b14bc24f-d056-49db-aae5-d5f89a70ac1d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:39","userId":"fa2e0179-720b-488f-bbd7-6a0bf65c2e93"}
{"contentLength":"1987","duration":"454ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:39","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"5f2a9eae-dda8-441c-aad1-a8be118ee405"}
{"contentLength":"1923","duration":"121ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:39","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b14bc24f-d056-49db-aae5-d5f89a70ac1d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:39","userId":"22434ff2-bdab-48a2-8647-52c7f666ef3a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:39","userId":"61bf8601-a17d-4f14-a55a-e70ce294118a"}
{"contentLength":"1987","duration":"127ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:39","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"fa2e0179-720b-488f-bbd7-6a0bf65c2e93"}
{"contentLength":"1871","duration":"18ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:39","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"22434ff2-bdab-48a2-8647-52c7f666ef3a"}
{"contentLength":"1987","duration":"18ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:39","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"61bf8601-a17d-4f14-a55a-e70ce294118a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:43","userId":"1ad639a5-e270-468c-8812-614a2bab70a3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:43","userId":"ad49e3f7-c00a-4952-b60d-8e2765f8d5ca"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:43","userId":"b3171539-8b8e-489f-9270-7433a8eedf05"}
{"contentLength":"1923","duration":"729ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"1ad639a5-e270-468c-8812-614a2bab70a3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:43","userId":"18586601-9760-4fa9-b35f-da5d6ce5b03d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:43","userId":"42ac0fda-69f1-4952-a883-a0c657f0f36e"}
{"contentLength":"1987","duration":"49ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ad49e3f7-c00a-4952-b60d-8e2765f8d5ca"}
{"contentLength":"1987","duration":"31ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"42ac0fda-69f1-4952-a883-a0c657f0f36e"}
{"contentLength":"1935","duration":"53ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b3171539-8b8e-489f-9270-7433a8eedf05"}
{"contentLength":"1987","duration":"42ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"18586601-9760-4fa9-b35f-da5d6ce5b03d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:47","userId":"8b7175ba-12a3-4d13-b41b-6d3530bf193e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:48","userId":"10860257-8cde-44b3-a43e-c459fdd59540"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:48","userId":"9aeed555-72f7-483a-bb0c-335548b5dadc"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:48","userId":"28fe8fe3-499c-46c2-85a5-585bda5af305"}
{"contentLength":"1987","duration":"445ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:48","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8b7175ba-12a3-4d13-b41b-6d3530bf193e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:48","userId":"39ab2388-989b-49b0-9d42-51e84e3f7385"}
{"contentLength":"1987","duration":"86ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:48","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"10860257-8cde-44b3-a43e-c459fdd59540"}
{"contentLength":"1987","duration":"86ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:48","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"9aeed555-72f7-483a-bb0c-335548b5dadc"}
{"contentLength":"1987","duration":"87ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:48","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"28fe8fe3-499c-46c2-85a5-585bda5af305"}
{"contentLength":"1923","duration":"18ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:48","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"39ab2388-989b-49b0-9d42-51e84e3f7385"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:51","userId":"7e1b8503-658f-4ed3-a571-ba714d3908cd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:52","userId":"093bd24d-f82d-4b03-b158-79bbeaa332a2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:52","userId":"cd5c3542-3b62-48f2-81b3-b1da10a51bf2"}
{"contentLength":"1987","duration":"677ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:52","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"7e1b8503-658f-4ed3-a571-ba714d3908cd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:52","userId":"8129f52f-888e-4706-b196-9a48a2333011"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:52","userId":"f15a4707-1465-423a-9811-26b381e2d14e"}
{"contentLength":"1987","duration":"31ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:52","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"093bd24d-f82d-4b03-b158-79bbeaa332a2"}
{"contentLength":"1931","duration":"33ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:52","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"cd5c3542-3b62-48f2-81b3-b1da10a51bf2"}
{"contentLength":"1987","duration":"36ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:52","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8129f52f-888e-4706-b196-9a48a2333011"}
{"contentLength":"1987","duration":"31ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:52","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"f15a4707-1465-423a-9811-26b381e2d14e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:56","userId":"d55327a2-fdb2-4270-8d7a-6711f9169735"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:56","userId":"98752385-43b0-48e6-b2cd-7785d74fbb09"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:56","userId":"679be605-256a-4bb6-8d61-dba6962aa19e"}
{"contentLength":"1935","duration":"443ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:56","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"d55327a2-fdb2-4270-8d7a-6711f9169735"}
{"contentLength":"1987","duration":"113ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:56","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"98752385-43b0-48e6-b2cd-7785d74fbb09"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:56","userId":"2fab1a73-6220-4a54-a610-1f8691e88e91"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:27:56","userId":"0842bff2-02e3-4450-846b-08955fc3573c"}
{"contentLength":"1871","duration":"120ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:56","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"679be605-256a-4bb6-8d61-dba6962aa19e"}
{"contentLength":"1987","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:56","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"2fab1a73-6220-4a54-a610-1f8691e88e91"}
{"contentLength":"1987","duration":"19ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:27:56","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"0842bff2-02e3-4450-846b-08955fc3573c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:00","userId":"d30f7b48-d3ff-44ba-9f43-8e07bb83f24d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:00","userId":"c7406a07-67e6-4c77-87d2-2e3ec70e32e8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:01","userId":"240286e3-f989-4fb4-b28b-dfa18249bf47"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:01","userId":"1a0e6970-28d9-48b7-a05b-4ad8293b18f2"}
{"contentLength":"1987","duration":"359ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:01","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"d30f7b48-d3ff-44ba-9f43-8e07bb83f24d"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:01","userId":"d5d58f64-55a6-4752-a74d-cc9383cb7f95"}
{"contentLength":"1987","duration":"329ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:01","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"c7406a07-67e6-4c77-87d2-2e3ec70e32e8"}
{"contentLength":"1923","duration":"81ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:01","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"240286e3-f989-4fb4-b28b-dfa18249bf47"}
{"contentLength":"1987","duration":"79ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:01","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"1a0e6970-28d9-48b7-a05b-4ad8293b18f2"}
{"contentLength":"1987","duration":"18ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:01","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"d5d58f64-55a6-4752-a74d-cc9383cb7f95"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:05","userId":"56566c67-0a70-4092-8b35-6db237dfa19c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:05","userId":"3de392b1-4595-4fca-846a-33c4561249f2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:05","userId":"353bba5a-1a9c-4faa-87f9-7e821f5c67d7"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:05","userId":"ba5483f0-f3fc-4c39-9c5c-6cf96420aa76"}
{"contentLength":"1987","duration":"253ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:05","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"3de392b1-4595-4fca-846a-33c4561249f2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:05","userId":"42a384cb-24cc-4ad7-b997-6608d544bf0f"}
{"contentLength":"1987","duration":"265ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:05","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"56566c67-0a70-4092-8b35-6db237dfa19c"}
{"contentLength":"1987","duration":"41ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:05","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"353bba5a-1a9c-4faa-87f9-7e821f5c67d7"}
{"contentLength":"1987","duration":"31ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:05","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ba5483f0-f3fc-4c39-9c5c-6cf96420aa76"}
{"contentLength":"1987","duration":"21ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:05","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"42a384cb-24cc-4ad7-b997-6608d544bf0f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:09","userId":"ea460167-46a9-4997-bcbc-ff089c2b5df8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:09","userId":"b9a9e646-2cd0-4973-9d81-b21afb156a70"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:09","userId":"152b6bf2-762a-4695-978e-c14719436952"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:09","userId":"8f453d8d-c12c-43ce-9826-9467180114c5"}
{"contentLength":"1987","duration":"695ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:09","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ea460167-46a9-4997-bcbc-ff089c2b5df8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:09","userId":"a77b24c3-024c-491c-8c6a-f5004f362f1c"}
{"contentLength":"1935","duration":"36ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:09","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"b9a9e646-2cd0-4973-9d81-b21afb156a70"}
{"contentLength":"1987","duration":"42ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:09","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"152b6bf2-762a-4695-978e-c14719436952"}
{"contentLength":"1987","duration":"38ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:09","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"8f453d8d-c12c-43ce-9826-9467180114c5"}
{"contentLength":"1987","duration":"33ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:09","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"a77b24c3-024c-491c-8c6a-f5004f362f1c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:13","userId":"3c480dfc-629f-46ab-a14c-55d94bc06dd2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:13","userId":"e70fb660-7afa-45a4-9b80-9c09768d0f6c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:13","userId":"81c7f6c4-d621-4c4c-8ce6-d12f5c603f28"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:13","userId":"ddb87e7a-eb29-4e50-a5bd-7cd9a8b33e99"}
{"contentLength":"1935","duration":"389ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:14","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"3c480dfc-629f-46ab-a14c-55d94bc06dd2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:28:14","userId":"0429c13a-a182-47e5-bb8c-7f04a2e5f8c2"}
{"contentLength":"1935","duration":"99ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:14","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"e70fb660-7afa-45a4-9b80-9c09768d0f6c"}
{"contentLength":"1923","duration":"103ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:14","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"81c7f6c4-d621-4c4c-8ce6-d12f5c603f28"}
{"contentLength":"1987","duration":"108ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:14","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ddb87e7a-eb29-4e50-a5bd-7cd9a8b33e99"}
{"contentLength":"1987","duration":"49ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 15:28:14","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"0429c13a-a182-47e5-bb8c-7f04a2e5f8c2"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:37:25","userId":"6d6acb89-4a89-4b35-8aec-0ab8d79652c0"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:37:25","userId":"04dc19ab-358f-4e10-bab7-2c3947e3e909"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:37:25","userId":"7f5216bd-b97f-460e-8cd3-14b65ed1f06f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:37:25","userId":"a3b74716-7ea6-4afb-9bfe-978dce92d527"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:37:25","userId":"3e7ba511-77de-41b6-9359-7d36848d9601"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:41:28","userId":"1563f576-187f-470a-a318-6ad4ac9bbffa"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:41:28","userId":"f70c2be4-d208-4444-82dc-5056a88bf952"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:41:28","userId":"37d91393-6ae7-4e39-9af2-a0cd6398674f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:41:28","userId":"fc4931cc-34f4-4ff4-9328-09b32ef14844"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:41:29","userId":"6bce1c36-9242-435a-b684-e94b3176aa41"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:46:09","userId":"2de4499e-f5aa-4d91-9d2b-0a561404b2a1"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:46:09","userId":"3b984ab6-e43f-4310-904f-6cb550fd8ed7"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:46:09","userId":"d17af4bc-6ab2-4b32-90de-b0c37f62e80e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:46:10","userId":"b71b80c7-5464-487c-8fda-2c4ad6753c78"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:46:10","userId":"5e9ad6cf-06bc-49c2-88b8-433a360695cd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:51:33","userId":"b5bb59da-54d9-4393-89a0-ea22aa160b70"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:51:34","userId":"3f1165bb-4fd5-48c9-b66c-3ec7527324cd"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:51:34","userId":"d43f38ad-027a-4f46-9a60-197ad280895c"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:51:34","userId":"7fc8cc23-0e74-429a-b1fa-8ff322c8212a"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 15:51:34","userId":"4393dffa-6637-4eb9-b567-d50fe4e77009"}
