{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:24:36"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:26:05"}
{"error":"password authentication failed for user \"postgres\"","level":"error","message":"Health check failed","service":"auth-service","status":"unhealthy","timestamp":"2025-07-15 17:28:18"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:28:50"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:31:57"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:28"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:46"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:32:58"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:33:26"}
{"email":"<EMAIL>","error":"password authentication failed for user \"postgres\"","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 17:33:27"}
{"error":"password authentication failed for user \"postgres\"","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd0a84f-2c03-4720-8c3b-f53fc6426413","service":"auth-service","stack":"SequelizeConnectionError: password authentication failed for user \"postgres\"\n    at Client._connectionCallback (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\postgres\\connection-manager.js:145:24)\n    at Client._handleErrorWhileConnecting (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:336:19)\n    at Client._handleErrorMessage (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\client.js:356:19)\n    at Connection.emit (node:events:518:28)\n    at D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg\\lib\\connection.js:116:12\n    at Parser.parse (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\parser.js:36:17)\n    at Socket.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\pg-protocol\\dist\\index.js:11:42)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)","timestamp":"2025-07-15 17:33:27","userAgent":"axios/1.10.0"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 17:34:43"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-15 18:47:58"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:19"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5a42651-a7c9-452a-bc44-f3254f28bbec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:19","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:08:41"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd5dc5d1-b2c6-41b0-9714-ab5590cac3fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:08:41","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:10:34"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e6432726-eb9a-4f2c-b73b-fb407665aa5c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:10:34","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:11:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bbebde79-15d3-4866-9c4f-f6d844bce977","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:11:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:02"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"91f4529c-1757-4be0-bbaf-aa837095bd42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:13:51"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"92ca9ade-1823-4b90-9b3d-9dca38f46107","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:13:51","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:14:45"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0aeb1e9d-7a9b-442b-8295-502a04eaa0fd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:14:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:16:17"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c9239e33-6180-4569-8e83-7a9ec1c2b6f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:16:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:20:58"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d0a44d01-d8de-491a-9b62-55017c0da149","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:20:58","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:27:52"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"612c9f01-812d-4b57-b089-5e71452dd96a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:27:52","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-15 23:34:09"}
{"error":"Email already exists","ip":"::ffff:**********","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"192d655f-d6fa-4cb1-a07f-8cf98cb6ec5b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (/app/src/services/authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async register (/app/src/controllers/authController.js:16:20)","timestamp":"2025-07-15 23:34:09","userAgent":"axios/1.10.0"}
{"error":"Email already exists","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fe9ffc0e-5be5-430a-ae54-b439a18f2e55","service":"auth-service","stack":"Error: Email already exists\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:97:50)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"error":"Invalid email or password","ip":"::ffff:127.0.0.1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"5c6d045a-d3a4-4d2e-9715-efa3d18df1e2","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.<anonymous> (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\tests\\auth.test.js:177:47)\n    at Promise.then.completed (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:298:28)\n    at new Promise (<anonymous>)\n    at callAsyncCircusFn (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\utils.js:231:10)\n    at _callCircusTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:316:40)\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at _runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:252:3)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:126:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at _runTestsForDescribeBlock (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:121:9)\n    at run (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\run.js:71:3)\n    at runAndTransformResultsToJestFormat (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapterInit.js:122:21)\n    at jestAdapter (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-circus\\build\\legacy-code-todo-rewrite\\jestAdapter.js:79:19)\n    at runTestInternal (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:367:16)\n    at runTest (D:\\(program-projects)\\A-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\jest-runner\\build\\runTest.js:444:34)","timestamp":"2025-07-16 07:56:26"}
{"database":"atma_db","error":"password authentication failed for user \"postgres\"","host":"localhost","level":"error","message":"Unable to connect to the database","service":"auth-service","timestamp":"2025-07-16 08:17:59"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-16 15:58:15"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6720fadf-736c-430b-8e35-f0e466e5cf9e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-16 15:58:15","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:17"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"84d3593c-9cca-4f53-b7b5-6520b0290890","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:17","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:50"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"3dcace34-f05e-4a7e-996b-8fa371059b56","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:50","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:15:53"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"23247aa0-b791-40cc-a778-ee9c583f69f7","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:15:53","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:32"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"bfaaaab6-c7ae-45d7-845b-31d2a4514395","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:32","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:36"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"2040f2b4-99c4-42f0-9f0d-aedd02646eba","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:36","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:18:49"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"ee122e38-7816-40f7-a693-8e3d47486bf4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:18:49","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-16 16:24:43"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"e3c3b757-d658-4824-a6d6-5f02c0d3ec4f","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-16 16:24:43","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-17 06:00:23"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"0404b631-1e62-4c16-87ce-5ee22f30db72","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:71:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-17 06:00:23","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:35:39","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"59f20432-3a02-4c8e-9e89-53b9d9dd2f13","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:35:39","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid value { username: 'superadmin' }","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:36:04","username":"superadmin"}
{"error":"Invalid value { username: 'superadmin' }","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"e1bf6768-182f-4b28-a069-e388f3e25203","service":"auth-service","stack":"Error: Invalid value { username: 'superadmin' }\n    at Object.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\sql-string.js:54:11)\n    at PostgresQueryGenerator.escape (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:766:22)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:71\n    at Array.map (<anonymous>)\n    at PostgresQueryGenerator._whereParseSingleValueObject (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1996:52)\n    at PostgresQueryGenerator.whereItemQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1812:19)\n    at D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1737:25\n    at Array.forEach (<anonymous>)\n    at PostgresQueryGenerator.whereItemsQuery (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:1735:35)\n    at PostgresQueryGenerator.getWhereConditions (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-generator.js:2078:19)","timestamp":"2025-07-18 05:36:04","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:39:59","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"4953c00f-1f82-4b2d-95ac-4856292b29e1","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:39:59","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"request aborted","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","service":"auth-service","stack":"BadRequestError: request aborted\n    at IncomingMessage.onAborted (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\node_modules\\raw-body\\index.js:245:10)\n    at IncomingMessage.emit (node:events:518:28)\n    at IncomingMessage._destroy (node:_http_incoming:221:10)\n    at _destroy (node:internal/streams/destroy:122:10)\n    at IncomingMessage.destroy (node:internal/streams/destroy:84:5)\n    at abortIncoming (node:_http_server:811:9)\n    at socketOnClose (node:_http_server:805:3)\n    at Socket.emit (node:events:530:35)\n    at TCP.<anonymous> (node:net:346:12)","timestamp":"2025-07-18 05:47:41","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:47:48","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"3a1e19ea-5604-462e-96fe-c51aed2ca653","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:47:48","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:49:10","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"0ef7e33c-665f-473e-b697-e6ec0fed6167","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:49:10","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"error":"Invalid username/email or password","level":"error","message":"Admin login failed","service":"auth-service","timestamp":"2025-07-18 05:53:45","username":"superadmin"}
{"error":"Invalid username/email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/admin/login","requestId":"a0a820b1-effb-426f-8500-c5d8de9a0f1f","service":"auth-service","stack":"Error: Invalid username/email or password\n    at Object.loginAdmin (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\adminService.js:126:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\adminController.js:65:20)","timestamp":"2025-07-18 05:53:45","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-18 09:10:06"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"ee296169-91ad-432f-a923-5dfe32a0b4e4","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:77:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-18 09:10:06","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 09:10:31"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"054cbcfe-2f00-45fd-a072-3694fadd06b2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 09:10:31","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-18 09:10:31"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"62bd736b-2f4f-4a19-b153-e01e1ba0fd28","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:77:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-18 09:10:31","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0ef2435c-4ea2-4e0f-9ba8-281a921a7ba7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd55f958-cc19-4b20-9c1d-3983554dc61d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9402f583-f30a-4a91-b829-2537ec7abc05","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"291d7ad2-4897-4976-ad82-aceb76460932","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"43649956-1d4f-4803-9c76-3a01bb1323a7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a9e4a65c-23f3-4218-aba4-618d5567a95a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"285753bc-1594-4bda-97c8-e122b9cec6a3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"55609144-1141-4edd-aa82-a1824a2e6095","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"71447a92-2103-42fa-890f-4daff663a832","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"0d9444d6-3c34-4504-999b-ad3f37daa025","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:29","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:43:59"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"52d8babc-c29f-42eb-be67-8cd9b129099f","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:43:59","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"email":"<EMAIL>","error":"Invalid email or password","level":"error","message":"User login failed","service":"auth-service","timestamp":"2025-07-18 12:44:05"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/login","requestId":"59bf7e4c-9b26-4874-83ab-ff4d973225cf","service":"auth-service","stack":"Error: Invalid email or password\n    at Object.loginUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:77:13)\n    at async login (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:46:20)","timestamp":"2025-07-18 12:44:05","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:29"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d4eba1ba-54ad-49f9-a741-65433e1ac591","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:29","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.4652"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"745e4812-7041-4083-b353-2686f5a649d1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bd3f901d-82ff-4b18-a808-a074cbc7f780","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fd7a173f-d572-4a75-9009-2af9f246e1b6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"aa38e16e-f507-4d7b-997d-24b5878eb12a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f4ebdf01-65c2-40e2-b9a6-68712b71b80a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"442ee8b6-bf44-4407-b057-0ec765712bd6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3b957855-1f87-4690-92c7-5839b58315ae","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7e8cf305-f82e-4b58-bd21-be42991c83a2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9591c0a9-45b2-4464-8bda-a4db118f71e2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:44:42"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"06ff4d76-397b-4ac3-85e6-070928502c4e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:44:42","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:45:01"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"42649b3f-3f38-489a-88bc-6b5addd6a9f5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:45:01","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:45:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"94669e86-f656-4a97-b999-1b1228f2ee94","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:45:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:45"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"5b5ad10b-2963-4003-8014-41843b62f341","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:45","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6127f828-a3cc-4af8-adcf-2447cac964b8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a36d578f-b21b-4721-8502-e5f1a890cb34","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f5cf3c42-5c72-41a3-b47d-8153fcf577bd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ecb3ac0c-0875-4191-a1f3-048f77397877","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cd6f8575-abbe-4c2e-924c-f3ec009209c6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"4cce95a7-70ec-4aa1-9034-aee815f51903","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a052873d-5de1-4fa2-bfc9-e4f7b6c6c4ca","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c902f060-f0cc-49f5-8ae9-593d3fa910fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bb6f65eb-1f9a-4dc2-8b48-a81d4dd43f9a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:46:53"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"18b27399-2a8f-403b-8e0a-f15e2a1c2cb7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:46:53","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"89387b03-653f-4d21-9c7b-9f2f14fc7cf8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b402d901-2fd6-4250-9fcf-f45e20d4662e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a42551be-4543-460f-9a61-a6ba059427aa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"dffea3c3-7e6d-460f-96bb-2ef8376d4587","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1c3c2e8c-7476-4872-8051-baa0dc339c2d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"acbd0921-7a04-4322-aacd-f953890e47e0","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"36794360-9288-41c2-80c5-3fb8a86603d6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f38fb972-7a7d-497c-b76c-b09bf375cac2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"dffc1aeb-f102-44bc-b118-5c938ef84f85","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:02"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c2b35061-b6f4-4873-8cc2-871cd62b52fa","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:02","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9af4807a-b702-4f65-9443-cd5a02e3ab94","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"449194a9-c4c6-45ee-be46-6ecaaf8e4b63","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"548376c3-9c27-4e20-8d4a-6a8cc0f938c9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"4957bafa-99ca-49b6-b344-19aac40989b9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"892517b7-e2e8-4e0c-bdce-c95dd2c730c6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"6571b1ad-f821-4370-8b18-7f25afc88300","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"191fc92a-fa69-4131-85aa-9519fe4813bb","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"fe40b867-9035-4b53-8feb-ae7ce2659405","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e87ea04d-a8a6-46c2-9e3b-8110b1fb3006","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3bd921d0-fe3e-4862-9f49-15f0f31d04c8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"09b54745-987b-4a32-897c-b6918ed7c1ce","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"23c93ed2-0212-44c7-bcfd-fd2beab44ea5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7dcb3548-dbfc-4ca6-b3ee-aad66c3534dc","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"306cf1e2-522d-4b58-ae20-519e6c6a1c76","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"734e52f7-d0a2-4d06-a2fd-4df40e5641c3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d4095cfc-27bf-44cb-a430-7f197bb83029","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9847872a-991d-4f80-8cf0-716880c1eaa5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a9c4c3e1-aadd-439d-a4c3-9a5d6880385c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e360ad3c-5a4f-41d0-8cab-fe8ee63b203a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1d65ee83-7293-47f5-92ec-c414f935757a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"59af6947-2348-4e85-a8e2-7f4de2a44cb3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ae7caf66-ad9e-4b3f-ae63-61ab9edbb85b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"be87e3cc-6487-4168-a50e-74abaabd5c41","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"275ff3f3-d909-49e3-8dfc-a2732ad0388c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"dbf49d89-6d9f-47cb-9c9b-fe78eea8d650","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"182f2b13-56c0-4e47-9436-cdee2971e27d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2fc8c6fe-4cf6-41f9-943f-1bda2b709d28","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"eeeb0141-c82b-43b2-b6fe-ccdcc59fd613","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ea3b7288-f546-4751-ad6c-3b7af4e02501","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"69381630-3f9e-4da2-b09e-3000e61e073a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b5d8f8f1-f94b-4b25-94e6-f66e63fa5d36","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e8eb5c50-a462-49b7-8bae-b8510796b9bd","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f61d175f-9704-4830-99a6-e02254374119","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d7935350-768a-475a-aff7-abb3cd153f30","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ab8a330e-9756-4c99-b927-9f56ead44ec4","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2d6600ec-a9f8-4c6c-abe7-843374a8ccc6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bc4fc568-cb93-4928-9ac0-5e7d7b6358b5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"04efb959-8fb1-4df2-aec0-72dc7931ae7c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3e614a2f-17ba-4417-9b79-343202ab9eec","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f7c30229-f87d-4cc9-896a-b14b3c238ed1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9ce32779-e6ab-41db-a6bd-167bb9d4d8d4","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7b338011-66f6-4b90-8ff3-e4efc7d7376e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e573b0cf-1751-4ab5-ba46-0d9d40b1723e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ff969bf7-0f74-431f-997a-b00f376b252e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"8c0e2482-1aa9-4272-bebc-375e438c572e","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"492467a6-dc85-4fdf-bc5f-dfd4952d332c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"994bd3ed-fa3c-41a0-adc8-8ebef0c7cae1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1e7b7e14-e10a-49c7-8221-d514fe72a956","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"bba2676b-7307-4db5-a772-325176618dbf","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2f66cfe8-f552-4d5e-8238-5db162791966","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a78a1b2c-80ac-4f41-b6ad-b4215cedbedb","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"edc918e1-6ef2-470d-8f82-84490a7c750b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"8aef20c9-585d-4fa8-bdec-43637dd5743c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"802be727-d739-4de1-a375-499d956efa28","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f99aee9d-43be-432a-a232-622c723d3347","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"553f8909-646a-4287-895b-4adf1e97e418","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ebb54782-4871-44c9-8beb-7571dd115346","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a9d5c0d1-a097-4194-8be3-4df722163565","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"417d0173-dd86-4eb4-9edf-c053386dd3f3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"443fa9e9-f4d9-4e62-bf73-b4f12e53298d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f45c89ea-ec1f-4728-bfd2-0cf6e40298f9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"a4249bb0-2dfc-4b7e-bafc-138884b74b69","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"25584a4c-54c0-439b-84da-78e316ba9236","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cbd25fd0-90ec-4367-948b-f0f0d6e7e6d9","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cb1d51ae-61a0-4a1b-a3d7-3a6c2b9ca3e3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"7f39954d-b46d-4254-86c2-40f282eb1091","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:17"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"c59991cd-f641-473e-a259-edae7ed79f8b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:17","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"271d26f0-f169-4351-90b7-7adf892909b2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"ab860ab8-3d78-4db7-93d2-263b896dee42","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"85385133-3443-4be9-bab4-02256b04c7ae","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"15d8f34e-90e3-4a29-9513-ce119db3993d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"f93f15c7-2d42-43a9-8028-86e8a065dc61","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"39cd876a-ebf8-4c4e-adf2-f25b88dd6e54","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"265f79eb-58e0-4760-9be6-9e2fcd835000","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"598266e0-2680-485b-88ec-c420950cecb2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3788287b-c9cb-4d4b-8376-0220fd4e007a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"673e18b6-bb0d-4d6e-80c1-f8f9815a7ac6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"575602eb-5e62-4d14-9b21-3bbd72f41bab","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"d26ca3f9-548d-403d-9758-7d832ad3920d","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2938b3d0-2cae-472d-92b3-7f0bacc011b7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"8f977109-cd9e-4f75-801e-fb4cd04f559b","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"3c06213e-3aef-4c54-868b-de4b1f67d2a0","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b7393d22-1569-4fb0-b553-1cfaf8071ff1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"457776a0-c33d-4f2f-b4fc-75b8f95507e7","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"865142c1-a27c-40aa-ba6f-6aa6821d8d2a","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:18"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"35b2774a-2fdc-44de-a15f-568de4de6ee8","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:18","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"cc105eef-8c90-4a65-a7f9-4d27b73c27cf","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"47f6c803-8d04-4c10-ac2b-6f5d2e18ba36","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2acf9802-8c91-44ac-b76c-d9b55db38630","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"b080fd9c-f0d2-474d-aa23-5536a4ea5dc2","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"e2afd5bf-8fca-4ef9-88fc-941132283db1","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"1cc6edc5-d334-450d-8ea4-dd78de37444c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"51a42505-8741-4759-a702-a2984f0721a5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"04bbed30-e696-41fe-aec2-ad6ef07aa449","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"9307afb9-cd04-40fb-bbf7-c278a68e2d62","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"4d37f6ef-37a6-47a4-bcfb-6af4a8520cd5","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"79ff5006-9396-42c1-8126-2a8282ea18e0","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"201f37ec-3890-4afc-8aed-fa4025f5e7f3","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"008a2acf-f89c-4a39-96e7-25c6f855c3c6","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:47:24"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"905974c1-c2fe-4639-9fd0-b9cb037c1822","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:47:24","userAgent":"axios/1.10.0"}
{"email":"<EMAIL>","error":"Email already exists","level":"error","message":"User registration failed","service":"auth-service","timestamp":"2025-07-18 12:52:43"}
{"error":"Email already exists","ip":"::1","level":"error","message":"Unhandled error occurred","method":"POST","path":"/auth/register","requestId":"2d5d58aa-043c-484f-8aad-9b45c83d9e3c","service":"auth-service","stack":"Error: Email already exists\n    at Object.registerUser (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\services\\authService.js:24:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async register (D:\\(program-projects)\\AI-Driven Talent Mapping Assessment\\atma-backend\\auth-service\\src\\controllers\\authController.js:16:20)","timestamp":"2025-07-18 12:52:43","userAgent":"axios/1.10.0"}
