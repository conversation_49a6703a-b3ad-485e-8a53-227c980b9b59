/**
 * Cleanup Test Data Script
 * 
 * This script removes test users and data created during load testing
 * to keep the database clean.
 */

const axios = require('axios');

const CONFIG = {
  API_BASE_URL: 'http://localhost:3000',
  ADMIN_EMAIL: '<EMAIL>',
  ADMIN_PASSWORD: 'admin123'
};

/**
 * Login as admin
 */
async function loginAdmin() {
  try {
    const response = await axios.post(`${CONFIG.API_BASE_URL}/admin/login`, {
      email: CONFIG.ADMIN_EMAIL,
      password: CONFIG.ADMIN_PASSWORD
    });
    
    return {
      success: true,
      token: response.data.data.token
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * Get all users (admin endpoint)
 */
async function getAllUsers(adminToken) {
  try {
    const response = await axios.get(`${CONFIG.API_BASE_URL}/admin/users`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    return {
      success: true,
      users: response.data.data.users || []
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * Delete user (admin endpoint)
 */
async function deleteUser(adminToken, userId) {
  try {
    const response = await axios.delete(`${CONFIG.API_BASE_URL}/admin/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${adminToken}`
      }
    });
    
    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data || error.message
    };
  }
}

/**
 * Main cleanup function
 */
async function cleanup() {
  console.log('🧹 ATMA Load Test Data Cleanup');
  console.log('==============================');
  console.log();
  
  // Login as admin
  console.log('🔐 Logging in as admin...');
  const loginResult = await loginAdmin();
  
  if (!loginResult.success) {
    console.log('❌ Failed to login as admin:', loginResult.error);
    console.log('💡 Make sure admin credentials are correct and services are running');
    return;
  }
  
  console.log('✅ Admin login successful');
  
  // Get all users
  console.log('📋 Fetching all users...');
  const usersResult = await getAllUsers(loginResult.token);
  
  if (!usersResult.success) {
    console.log('❌ Failed to fetch users:', usersResult.error);
    return;
  }
  
  const allUsers = usersResult.users;
  console.log(`📊 Found ${allUsers.length} total users`);
  
  // Filter test users (those with loadtest_ prefix)
  const testUsers = allUsers.filter(user => 
    user.email && user.email.includes('loadtest_')
  );
  
  console.log(`🎯 Found ${testUsers.length} test users to cleanup`);
  
  if (testUsers.length === 0) {
    console.log('✅ No test users found. Database is clean!');
    return;
  }
  
  // Confirm deletion
  console.log();
  console.log('Test users to be deleted:');
  testUsers.forEach((user, index) => {
    console.log(`  ${index + 1}. ${user.email} (ID: ${user.id})`);
  });
  
  console.log();
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const answer = await new Promise(resolve => {
    readline.question('⚠️  Are you sure you want to delete these test users? (y/N): ', resolve);
  });
  
  readline.close();
  
  if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
    console.log('❌ Cleanup cancelled');
    return;
  }
  
  // Delete test users
  console.log();
  console.log('🗑️  Deleting test users...');
  
  let deletedCount = 0;
  let errorCount = 0;
  
  for (const user of testUsers) {
    process.stdout.write(`Deleting ${user.email}... `);
    
    const deleteResult = await deleteUser(loginResult.token, user.id);
    
    if (deleteResult.success) {
      console.log('✅');
      deletedCount++;
    } else {
      console.log('❌', deleteResult.error);
      errorCount++;
    }
    
    // Small delay to avoid overwhelming the API
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log();
  console.log('==============================');
  console.log('🏁 Cleanup Complete!');
  console.log('==============================');
  console.log(`✅ Successfully deleted: ${deletedCount} users`);
  console.log(`❌ Failed to delete: ${errorCount} users`);
  console.log(`📊 Total processed: ${testUsers.length} users`);
  
  if (errorCount > 0) {
    console.log();
    console.log('💡 Some users could not be deleted. This might be due to:');
    console.log('   - Foreign key constraints (assessment results)');
    console.log('   - Database locks');
    console.log('   - Permission issues');
    console.log('   - Try running the cleanup again later');
  }
}

/**
 * Check system health
 */
async function checkHealth() {
  try {
    const response = await axios.get(`${CONFIG.API_BASE_URL}/health/live`, {
      timeout: 5000
    });
    return response.status === 200;
  } catch (error) {
    return false;
  }
}

// Main execution
async function main() {
  // Check if system is running
  const isHealthy = await checkHealth();
  if (!isHealthy) {
    console.log('❌ ATMA services are not running');
    console.log('💡 Please start services first: start-all-simple.bat');
    process.exit(1);
  }
  
  await cleanup();
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('\n💥 Unexpected error:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (error) => {
  console.error('\n💥 Unhandled promise rejection:', error.message);
  process.exit(1);
});

// Run cleanup
if (require.main === module) {
  main().catch(error => {
    console.error('\n💥 Cleanup failed:', error.message);
    process.exit(1);
  });
}

module.exports = { cleanup, checkHealth };
