@echo off
title Configure Mock AI for Load Testing
echo ========================================
echo Configure Mock AI for Load Testing
echo ========================================
echo.

echo 🔧 Configuring analysis workers to use Mock AI...
echo.

REM Check if analysis-worker directory exists
if not exist "analysis-worker" (
    echo ❌ analysis-worker directory not found
    echo Make sure you're running this from the atma-backend root directory
    pause
    exit /b 1
)

REM Backup original .env if it exists
if exist "analysis-worker\.env" (
    echo 📋 Backing up original .env file...
    copy "analysis-worker\.env" "analysis-worker\.env.backup" >nul
    echo ✅ Backup created: analysis-worker\.env.backup
)

REM Create or update .env file for mock AI
echo 📝 Creating .env configuration for Mock AI...

(
echo # Environment
echo NODE_ENV=development
echo.
echo # RabbitMQ Configuration
echo RABBITMQ_URL=amqp://localhost:5672
echo RABBITMQ_USER=guest
echo RABBITMQ_PASSWORD=guest
echo QUEUE_NAME=assessment_analysis
echo EXCHANGE_NAME=atma_exchange
echo ROUTING_KEY=analysis.process
echo DEAD_LETTER_QUEUE=assessment_analysis_dlq
echo.
echo # Queue Configuration
echo QUEUE_DURABLE=true
echo MESSAGE_PERSISTENT=true
echo.
echo # Google Generative AI Configuration
echo GOOGLE_AI_API_KEY=test_api_key_placeholder
echo GOOGLE_AI_MODEL=gemini-2.5-flash
echo AI_TEMPERATURE=0.7
echo AI_MAX_TOKENS=4096
echo.
echo # Mock AI Configuration ^(ENABLED FOR LOAD TESTING^)
echo USE_MOCK_MODEL=true
echo.
echo # Archive Service Configuration
echo ARCHIVE_SERVICE_URL=http://localhost:3002
echo ARCHIVE_SERVICE_KEY=your_archive_service_key_here
echo.
echo # Notification Service Configuration
echo NOTIFICATION_SERVICE_URL=http://localhost:3005
echo NOTIFICATION_SERVICE_KEY=your_notification_service_key_here
echo.
echo # Worker Configuration
echo WORKER_CONCURRENCY=5
echo WORKER_PREFETCH=1
echo.
echo # Retry Configuration
echo MAX_RETRIES=3
echo RETRY_DELAY=5000
echo.
echo # Timeout Configuration
echo PROCESSING_TIMEOUT=300000
echo AI_REQUEST_TIMEOUT=60000
echo ARCHIVE_REQUEST_TIMEOUT=30000
echo NOTIFICATION_REQUEST_TIMEOUT=10000
) > "analysis-worker\.env"

echo ✅ Mock AI configuration created successfully!
echo.
echo 📊 Configuration Summary:
echo   - USE_MOCK_MODEL=true
echo   - GOOGLE_AI_API_KEY=test_api_key_placeholder
echo   - Mock responses will be used instead of real Gemini API
echo   - Processing will be faster and won't consume API credits
echo.
echo 🔄 To restore original configuration:
echo   1. Stop all services
echo   2. Copy analysis-worker\.env.backup to analysis-worker\.env
echo   3. Restart services
echo.
echo ✅ Ready for load testing!
echo.
pause
