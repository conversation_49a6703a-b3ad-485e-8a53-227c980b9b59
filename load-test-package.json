{"name": "atma-load-test", "version": "1.0.0", "description": "End-to-End Load Testing for ATMA Backend", "main": "load-test-e2e.js", "scripts": {"test": "node load-test-e2e.js", "test:small": "node load-test-e2e.js 10 500", "test:medium": "node load-test-e2e.js 50 1000", "test:large": "node load-test-e2e.js 100 1500", "test:stress": "node load-test-e2e.js 200 2000", "install-deps": "npm install", "setup": "npm install && echo 'Setup complete! Run npm run test to start load testing.'"}, "dependencies": {"axios": "^1.6.0"}, "keywords": ["load-testing", "e2e", "atma", "backend", "performance"], "author": "ATMA Team", "license": "MIT"}