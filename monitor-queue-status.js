/**
 * Queue Status Monitor
 * 
 * Script untuk monitoring status RabbitMQ queue dan worker performance
 */

const axios = require('axios');

// Configuration
const CONFIG = {
  RABBITMQ_MANAGEMENT_URL: process.env.RABBITMQ_MANAGEMENT_URL || 'http://localhost:15672',
  RABBITMQ_USER: process.env.RABBITMQ_USER || 'guest',
  RABBITMQ_PASSWORD: process.env.RABBITMQ_PASSWORD || 'guest',
  QUEUE_NAME: 'assessment_analysis',
  DLQ_NAME: 'assessment_analysis_dlq',
  MONITOR_INTERVAL: 5000, // 5 seconds
  API_GATEWAY_URL: process.env.API_GATEWAY_URL || 'http://localhost:3000'
};

// Create axios instance for RabbitMQ Management API
const rabbitClient = axios.create({
  baseURL: CONFIG.RABBITMQ_MANAGEMENT_URL,
  auth: {
    username: CONFIG.RABBITMQ_USER,
    password: CONFIG.RABBITMQ_PASSWORD
  },
  timeout: 5000
});

/**
 * Get queue information
 */
async function getQueueInfo() {
  try {
    const response = await rabbitClient.get(`/api/queues/%2F/${CONFIG.QUEUE_NAME}`);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to get queue info:', error.message);
    return null;
  }
}

/**
 * Get dead letter queue information
 */
async function getDLQInfo() {
  try {
    const response = await rabbitClient.get(`/api/queues/%2F/${CONFIG.DLQ_NAME}`);
    return response.data;
  } catch (error) {
    // DLQ might not exist yet, that's okay
    return { messages: 0, name: CONFIG.DLQ_NAME };
  }
}

/**
 * Get consumer information
 */
async function getConsumerInfo() {
  try {
    const response = await rabbitClient.get(`/api/queues/%2F/${CONFIG.QUEUE_NAME}`);
    return {
      consumers: response.data.consumers || 0,
      consumerUtilisation: response.data.consumer_utilisation || 0
    };
  } catch (error) {
    console.error('❌ Failed to get consumer info:', error.message);
    return { consumers: 0, consumerUtilisation: 0 };
  }
}

/**
 * Check API Gateway health
 */
async function checkAPIHealth() {
  try {
    const response = await axios.get(`${CONFIG.API_GATEWAY_URL}/api/health`, { timeout: 3000 });
    return response.status === 200;
  } catch (error) {
    return false;
  }
}

/**
 * Display queue status
 */
function displayQueueStatus(queueInfo, dlqInfo, consumerInfo, apiHealth) {
  // Clear console
  console.clear();
  
  console.log('🔍 ATMA Backend - Queue Status Monitor');
  console.log('='.repeat(60));
  console.log(`📅 ${new Date().toLocaleString()}`);
  console.log('');
  
  // API Gateway Status
  console.log('🌐 API Gateway Status:');
  console.log(`   Health: ${apiHealth ? '✅ Healthy' : '❌ Unhealthy'}`);
  console.log('');
  
  if (queueInfo) {
    // Main Queue Status
    console.log('📋 Main Queue Status:');
    console.log(`   Queue Name: ${queueInfo.name}`);
    console.log(`   Messages Ready: ${queueInfo.messages_ready || 0}`);
    console.log(`   Messages Unacknowledged: ${queueInfo.messages_unacknowledged || 0}`);
    console.log(`   Total Messages: ${queueInfo.messages || 0}`);
    console.log(`   Message Rate: ${(queueInfo.message_stats?.publish_details?.rate || 0).toFixed(2)}/sec`);
    console.log(`   Delivery Rate: ${(queueInfo.message_stats?.deliver_get_details?.rate || 0).toFixed(2)}/sec`);
    console.log('');
    
    // Consumer Status
    console.log('👥 Consumer Status:');
    console.log(`   Active Consumers: ${consumerInfo.consumers}`);
    console.log(`   Consumer Utilisation: ${(consumerInfo.consumerUtilisation * 100).toFixed(1)}%`);
    console.log('');
    
    // Queue Health Analysis
    console.log('📊 Queue Health Analysis:');
    const messagesReady = queueInfo.messages_ready || 0;
    const messagesUnack = queueInfo.messages_unacknowledged || 0;
    const totalMessages = queueInfo.messages || 0;
    
    if (totalMessages === 0) {
      console.log('   ✅ Queue is empty - No pending jobs');
    } else if (messagesReady > 50) {
      console.log('   ⚠️  High queue backlog - Consider scaling workers');
    } else if (messagesUnack > 25) {
      console.log('   ⚠️  Many unacknowledged messages - Workers may be overloaded');
    } else {
      console.log('   ✅ Queue is healthy - Normal processing');
    }
    
    // Processing Capacity Analysis
    const expectedCapacity = consumerInfo.consumers * 5; // Assuming 5 concurrency per worker
    console.log(`   Expected Capacity: ${expectedCapacity} concurrent jobs`);
    console.log(`   Current Load: ${messagesUnack} jobs processing`);
    console.log(`   Utilization: ${expectedCapacity > 0 ? ((messagesUnack / expectedCapacity) * 100).toFixed(1) : 0}%`);
    
  } else {
    console.log('❌ Unable to connect to RabbitMQ Management API');
  }
  
  console.log('');
  
  // Dead Letter Queue Status
  console.log('💀 Dead Letter Queue Status:');
  if (dlqInfo) {
    console.log(`   Failed Messages: ${dlqInfo.messages || 0}`);
    if (dlqInfo.messages > 0) {
      console.log('   ⚠️  Some messages failed processing - Check worker logs');
    } else {
      console.log('   ✅ No failed messages');
    }
  } else {
    console.log('   ✅ No dead letter queue (no failures)');
  }
  
  console.log('');
  console.log('🔄 Refreshing every 5 seconds... (Press Ctrl+C to stop)');
  console.log('='.repeat(60));
}

/**
 * Start monitoring
 */
async function startMonitoring() {
  console.log('🚀 Starting queue status monitoring...');
  console.log('📡 RabbitMQ Management:', CONFIG.RABBITMQ_MANAGEMENT_URL);
  console.log('📋 Queue Name:', CONFIG.QUEUE_NAME);
  console.log('');
  
  const monitor = async () => {
    try {
      const [queueInfo, dlqInfo, consumerInfo, apiHealth] = await Promise.all([
        getQueueInfo(),
        getDLQInfo(),
        getConsumerInfo(),
        checkAPIHealth()
      ]);
      
      displayQueueStatus(queueInfo, dlqInfo, consumerInfo, apiHealth);
    } catch (error) {
      console.error('❌ Monitoring error:', error.message);
    }
  };
  
  // Initial check
  await monitor();
  
  // Set up interval
  const intervalId = setInterval(monitor, CONFIG.MONITOR_INTERVAL);
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n⏹️  Stopping queue monitor...');
    clearInterval(intervalId);
    process.exit(0);
  });
  
  process.on('SIGTERM', () => {
    console.log('\n⏹️  Stopping queue monitor...');
    clearInterval(intervalId);
    process.exit(0);
  });
}

/**
 * Show queue statistics summary
 */
async function showQueueSummary() {
  try {
    const [queueInfo, dlqInfo, consumerInfo] = await Promise.all([
      getQueueInfo(),
      getDLQInfo(),
      getConsumerInfo()
    ]);
    
    console.log('📊 Queue Summary:');
    console.log('='.repeat(40));
    
    if (queueInfo) {
      console.log(`Total Messages: ${queueInfo.messages || 0}`);
      console.log(`Messages Ready: ${queueInfo.messages_ready || 0}`);
      console.log(`Messages Processing: ${queueInfo.messages_unacknowledged || 0}`);
      console.log(`Active Consumers: ${consumerInfo.consumers}`);
      console.log(`Failed Messages: ${dlqInfo?.messages || 0}`);
      
      const publishRate = queueInfo.message_stats?.publish_details?.rate || 0;
      const deliveryRate = queueInfo.message_stats?.deliver_get_details?.rate || 0;
      
      console.log(`Publish Rate: ${publishRate.toFixed(2)}/sec`);
      console.log(`Delivery Rate: ${deliveryRate.toFixed(2)}/sec`);
      
      if (publishRate > deliveryRate && queueInfo.messages_ready > 0) {
        console.log('⚠️  Queue is backing up - Consider scaling workers');
      } else {
        console.log('✅ Queue processing is keeping up');
      }
    } else {
      console.log('❌ Unable to get queue information');
    }
    
  } catch (error) {
    console.error('❌ Failed to get queue summary:', error.message);
  }
}

// Export functions for use in other scripts
module.exports = {
  getQueueInfo,
  getDLQInfo,
  getConsumerInfo,
  showQueueSummary,
  startMonitoring
};

// Run if called directly
if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'summary') {
    showQueueSummary().then(() => process.exit(0));
  } else {
    startMonitoring();
  }
}
