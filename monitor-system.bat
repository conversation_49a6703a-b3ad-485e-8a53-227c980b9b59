@echo off
title ATMA System Monitor
echo ========================================
echo ATMA System Resource Monitor
echo ========================================
echo.

echo 🖥️  System Resource Monitoring
echo Press Ctrl+C to stop monitoring
echo.

:monitor_loop

echo ========================================
echo %date% %time%
echo ========================================

REM CPU Usage
echo 💻 CPU Usage:
wmic cpu get loadpercentage /value | findstr "LoadPercentage"

echo.

REM Memory Usage  
echo 🧠 Memory Usage:
for /f "skip=1" %%i in ('wmic OS get TotalVisibleMemorySize /value') do set TotalMem=%%i
for /f "skip=1" %%i in ('wmic OS get FreePhysicalMemory /value') do set FreeMem=%%i

set TotalMem=%TotalMem:~19%
set FreeMem=%FreeMem:~17%

set /a UsedMem=%TotalMem%-%FreeMem%
set /a MemPercent=(%UsedMem%*100)/%TotalMem%

echo Total Memory: %TotalMem% KB
echo Used Memory: %UsedMem% KB (%MemPercent%%%)
echo Free Memory: %FreeMem% KB

echo.

REM Disk Usage
echo 💾 Disk Usage (C: Drive):
for /f "tokens=3" %%i in ('dir c:\ /-c ^| find "bytes free"') do set FreeSpace=%%i
echo Free Space: %FreeSpace% bytes

echo.

REM Network Connections
echo 🌐 Active Network Connections (ATMA Ports):
echo Port 3000 (API Gateway):
netstat -an | findstr ":3000 " | find /c "LISTENING" > temp_count.txt
set /p count3000=<temp_count.txt
if %count3000% gtr 0 (echo   ✅ Listening) else (echo   ❌ Not Listening)

echo Port 3001 (Auth Service):
netstat -an | findstr ":3001 " | find /c "LISTENING" > temp_count.txt
set /p count3001=<temp_count.txt
if %count3001% gtr 0 (echo   ✅ Listening) else (echo   ❌ Not Listening)

echo Port 3002 (Archive Service):
netstat -an | findstr ":3002 " | find /c "LISTENING" > temp_count.txt
set /p count3002=<temp_count.txt
if %count3002% gtr 0 (echo   ✅ Listening) else (echo   ❌ Not Listening)

echo Port 3003 (Assessment Service):
netstat -an | findstr ":3003 " | find /c "LISTENING" > temp_count.txt
set /p count3003=<temp_count.txt
if %count3003% gtr 0 (echo   ✅ Listening) else (echo   ❌ Not Listening)

echo Port 3005 (Notification Service):
netstat -an | findstr ":3005 " | find /c "LISTENING" > temp_count.txt
set /p count3005=<temp_count.txt
if %count3005% gtr 0 (echo   ✅ Listening) else (echo   ❌ Not Listening)

del temp_count.txt 2>nul

echo.

REM Node.js Processes
echo 🟢 Node.js Processes:
for /f %%i in ('tasklist /fi "imagename eq node.exe" 2^>nul ^| find /c "node.exe"') do set NodeCount=%%i
echo Active Node.js processes: %NodeCount%

echo.

REM RabbitMQ Status (if running)
echo 🐰 RabbitMQ Status:
tasklist /fi "imagename eq erl.exe" 2>nul | find "erl.exe" >nul
if %errorlevel% equ 0 (
    echo   ✅ RabbitMQ is running
) else (
    echo   ❌ RabbitMQ not detected
)

echo.

REM PostgreSQL Status (if running)
echo 🐘 PostgreSQL Status:
tasklist /fi "imagename eq postgres.exe" 2>nul | find "postgres.exe" >nul
if %errorlevel% equ 0 (
    echo   ✅ PostgreSQL is running
) else (
    echo   ❌ PostgreSQL not detected
)

echo.
echo ⏳ Next update in 10 seconds... (Ctrl+C to stop)
timeout /t 10 /nobreak >nul

goto monitor_loop
