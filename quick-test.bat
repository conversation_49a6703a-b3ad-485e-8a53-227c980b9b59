@echo off
title ATMA Quick Test - 5 Users
echo ========================================
echo ATMA Quick Test - 5 Users
echo ========================================
echo.

echo 🎯 This is a quick test with 5 users to verify:
echo   - System is working properly
echo   - Assessment data format is correct
echo   - All services are responding
echo   - Database saves are working
echo.

REM Check if services are running
echo 🔍 Checking if ATMA services are running...
netstat -an | findstr ":3000 " >nul
if %errorlevel% neq 0 (
    echo ❌ API Gateway (port 3000) is not running
    echo.
    echo Please start all services first:
    echo   1. Run: start-all-simple.bat
    echo   2. Wait for all services to start
    echo   3. Then run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ API Gateway is running
echo.

REM Check dependencies
echo 📦 Checking dependencies...
node -e "require('axios')" 2>nul
if %errorlevel% neq 0 (
    echo ❌ Dependencies not installed
    echo Installing axios...
    npm install axios
    if %errorlevel% neq 0 (
        echo ❌ Failed to install dependencies
        pause
        exit /b 1
    )
)
echo ✅ Dependencies are ready

echo.
echo 🧪 Running assessment data validation...
node test-assessment-validation.js >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Assessment data validation failed
    echo Please check test-assessment-validation.js for details
    pause
    exit /b 1
)
echo ✅ Assessment data validation passed

echo.
echo 🚀 Starting Quick Test (5 users)...
echo.
echo 📊 Test Configuration:
echo   - Users: 5 concurrent
echo   - Batch Size: 2 users per batch
echo   - Delay: 1000ms between batches
echo   - Expected Duration: ~2-3 minutes
echo.

REM Run quick test
node load-test-e2e.js 5 1000

echo.
echo ========================================
echo Quick Test Complete!
echo ========================================
echo.

if %errorlevel% equ 0 (
    echo ✅ Quick test completed successfully!
    echo.
    echo 📊 If the results show:
    echo   - Success rate ≥80%% - System is ready for larger tests
    echo   - Success rate 60-79%% - Some optimization needed
    echo   - Success rate <60%% - Check system configuration
    echo.
    echo 🚀 Ready for larger tests:
    echo   - run-load-test.bat (interactive menu)
    echo   - run-100-user-test.bat (full 100 user test)
    echo   - node load-test-e2e.js 50 1000 (50 user test)
) else (
    echo ❌ Quick test encountered issues
    echo.
    echo 🔍 Troubleshooting steps:
    echo   1. Check if all services are running properly
    echo   2. Check system resources (CPU, RAM)
    echo   3. Verify database connectivity
    echo   4. Check service logs for errors
    echo.
    echo 💡 Try running the test again or check individual services
)

echo.
pause
