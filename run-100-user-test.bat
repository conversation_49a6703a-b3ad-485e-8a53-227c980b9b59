@echo off
title ATMA 100 User Load Test - Complete Setup and Run
echo ========================================
echo ATMA 100 User Load Test
echo Complete Setup and Execution
echo ========================================
echo.

echo 🎯 This script will:
echo   1. Setup load test dependencies
echo   2. Configure Mock AI for fast testing
echo   3. Check if ATMA services are running
echo   4. Run 100 concurrent user load test
echo   5. Generate comprehensive report
echo.

set /p confirm="Continue with 100 user load test? (y/n): "
if /i not "%confirm%"=="y" (
    echo Test cancelled.
    pause
    exit /b 0
)

echo.
echo ========================================
echo Step 1: Setup Dependencies
echo ========================================

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js first.
    pause
    exit /b 1
)
echo ✅ Node.js is available

REM Install dependencies
echo 📦 Installing axios dependency...
npm install axios >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)
echo ✅ Dependencies installed

echo.
echo 🧪 Running assessment data validation...
node test-assessment-validation.js >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Assessment data validation failed
    pause
    exit /b 1
)
echo ✅ Assessment data validation passed

echo.
echo ========================================
echo Step 2: Configure Mock AI
echo ========================================

REM Configure Mock AI
call configure-mock-ai.bat >nul
echo ✅ Mock AI configured for fast testing

echo.
echo ========================================
echo Step 3: Check ATMA Services
echo ========================================

echo 🔍 Checking if ATMA services are running...

REM Check API Gateway
netstat -an | findstr ":3000 " >nul
if %errorlevel% neq 0 (
    echo ❌ API Gateway (port 3000) is not running
    echo.
    echo 🚀 Starting ATMA services automatically...
    echo Please wait while services start up...
    
    start "ATMA Services" cmd /k "start-all-simple.bat"
    
    echo ⏳ Waiting 30 seconds for services to initialize...
    timeout /t 30 /nobreak >nul
    
    REM Check again
    netstat -an | findstr ":3000 " >nul
    if %errorlevel% neq 0 (
        echo ❌ Services failed to start automatically
        echo Please manually run: start-all-simple.bat
        echo Then run this script again
        pause
        exit /b 1
    )
)

echo ✅ API Gateway is running
echo ✅ ATMA services are ready

echo.
echo ========================================
echo Step 4: Pre-Test System Check
echo ========================================

echo 🔍 Performing system health check...
node -e "
const axios = require('axios');
axios.get('http://localhost:3000/health/live', {timeout: 10000})
  .then(res => {
    console.log('✅ System health check passed');
    process.exit(0);
  })
  .catch(err => {
    console.log('❌ System health check failed:', err.message);
    process.exit(1);
  });
" 2>nul

if %errorlevel% neq 0 (
    echo ❌ System health check failed
    echo Please check if all services are properly running
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 5: Running 100 User Load Test
echo ========================================
echo.
echo 🚀 Starting 100 concurrent user load test...
echo 📊 Test Configuration:
echo   - Users: 100 concurrent
echo   - Batch Size: 5 users per batch
echo   - Delay: 1500ms between batches
echo   - Mock AI: Enabled (fast processing)
echo   - Max Wait: 5 minutes per user
echo.
echo ⏳ This test will take approximately 5-10 minutes...
echo 📈 Watch for real-time progress updates below:
echo.

REM Run the actual load test
node load-test-e2e.js 100 1500

echo.
echo ========================================
echo Test Complete!
echo ========================================
echo.

if %errorlevel% equ 0 (
    echo ✅ Load test completed successfully!
    echo.
    echo 📊 Check the results above for:
    echo   - Success rate (target: ≥90%%)
    echo   - End-to-end timing (target: ≤60s average)
    echo   - Error analysis and recommendations
    echo.
    echo 💡 If success rate is below 90%%, consider:
    echo   - Increasing worker capacity
    echo   - Optimizing database performance
    echo   - Checking system resources
) else (
    echo ❌ Load test encountered issues
    echo Check the error messages above for details
)

echo.
echo 🔄 To run another test:
echo   - run-load-test.bat (interactive menu)
echo   - node load-test-e2e.js [users] [delay]
echo.
echo 📋 To restore original AI configuration:
echo   - Stop services
echo   - Copy analysis-worker\.env.backup to analysis-worker\.env  
echo   - Restart services
echo.
pause
