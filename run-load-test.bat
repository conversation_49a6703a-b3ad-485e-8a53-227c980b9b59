@echo off
title ATMA Load Test Runner
echo ========================================
echo ATMA Backend Load Test Runner
echo ========================================
echo.

REM Check if services are running
echo 🔍 Checking if ATMA services are running...
netstat -an | findstr ":3000 " >nul
if %errorlevel% neq 0 (
    echo ❌ API Gateway (port 3000) is not running
    echo.
    echo Please start all services first:
    echo   1. Run: start-all-simple.bat
    echo   2. Wait for all services to start
    echo   3. Then run this script again
    echo.
    pause
    exit /b 1
)

echo ✅ API Gateway is running on port 3000
echo.

REM Show test options
echo 📊 Available Load Tests:
echo.
echo   1. Small Test    - 10 users  (recommended for first test)
echo   2. Medium Test   - 50 users  (moderate load)
echo   3. Large Test    - 100 users (heavy load - target test)
echo   4. Stress Test   - 200 users (stress test)
echo   5. Custom Test   - Enter your own parameters
echo.

set /p choice="Choose test type (1-5): "

if "%choice%"=="1" (
    echo.
    echo 🚀 Running Small Test (10 users)...
    node load-test-e2e.js 10 500
) else if "%choice%"=="2" (
    echo.
    echo 🚀 Running Medium Test (50 users)...
    node load-test-e2e.js 50 1000
) else if "%choice%"=="3" (
    echo.
    echo 🚀 Running Large Test (100 users - TARGET TEST)...
    node load-test-e2e.js 100 1500
) else if "%choice%"=="4" (
    echo.
    echo 🚀 Running Stress Test (200 users)...
    echo ⚠️  This is a stress test - system may struggle!
    node load-test-e2e.js 200 2000
) else if "%choice%"=="5" (
    echo.
    set /p users="Enter number of concurrent users: "
    set /p delay="Enter delay between batches (ms): "
    echo.
    echo 🚀 Running Custom Test (%users% users, %delay%ms delay)...
    node load-test-e2e.js %users% %delay%
) else (
    echo.
    echo ❌ Invalid choice. Running default test (10 users)...
    node load-test-e2e.js 10 500
)

echo.
echo ========================================
echo Load Test Complete!
echo ========================================
echo.
pause
