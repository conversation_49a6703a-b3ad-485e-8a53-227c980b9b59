@echo off
title ATMA Load Test Setup
echo ========================================
echo ATMA Backend Load Test Setup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js is available
node --version

echo.
echo 📦 Installing dependencies...
npm install axios

if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo.
echo ✅ Dependencies installed successfully!
echo.
echo 🧪 Running assessment data validation test...
node test-assessment-validation.js

if %errorlevel% neq 0 (
    echo ❌ Assessment data validation failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo Setup Complete!
echo ========================================
echo.
echo 🚀 You can now run load tests:
echo.
echo Basic Commands:
echo   node load-test-e2e.js                    - Default test (10 users)
echo   node load-test-e2e.js 50 1000           - 50 users with 1s delay
echo   node load-test-e2e.js 100 1500          - 100 users with 1.5s delay
echo.
echo Pre-configured Tests:
echo   npm run test:small                       - 10 users (light test)
echo   npm run test:medium                      - 50 users (medium test)  
echo   npm run test:large                       - 100 users (heavy test)
echo   npm run test:stress                      - 200 users (stress test)
echo.
echo ⚠️  IMPORTANT: Make sure all ATMA services are running first!
echo    Run: start-all-simple.bat
echo.
pause
