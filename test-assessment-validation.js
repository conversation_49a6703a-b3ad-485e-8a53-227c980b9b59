/**
 * Test Assessment Data Validation
 * 
 * This script tests if our assessment data format is valid
 * before running the full load test.
 */

const axios = require('axios');

const CONFIG = {
  API_BASE_URL: 'http://localhost:3000'
};

// Sample assessment data (same as in load-test-e2e.js)
const SAMPLE_ASSESSMENT = {
  riasec: {
    realistic: 75,
    investigative: 85,
    artistic: 60,
    social: 50,
    enterprising: 70,
    conventional: 55
  },
  ocean: {
    openness: 80,
    conscientiousness: 65,
    extraversion: 55,
    agreeableness: 45,
    neuroticism: 30
  },
  viaIs: {
    creativity: 85,
    curiosity: 78,
    judgment: 70,
    loveOfLearning: 82,
    perspective: 60,
    bravery: 55,
    perseverance: 68,
    honesty: 73,
    zest: 66,
    love: 80,
    kindness: 75,
    socialIntelligence: 65,
    teamwork: 60,
    fairness: 70,
    leadership: 67,
    forgiveness: 58,
    humility: 62,
    prudence: 69,
    selfRegulation: 61,
    appreciationOfBeauty: 50,
    gratitude: 72,
    hope: 77,
    humor: 65,
    spirituality: 55
  }
};

/**
 * Generate random assessment data with integer values
 */
function generateRandomAssessment() {
  const assessment = JSON.parse(JSON.stringify(SAMPLE_ASSESSMENT));
  
  // Add random variations (±20 points) and ensure integers
  Object.keys(assessment.riasec).forEach(key => {
    assessment.riasec[key] = Math.round(Math.max(0, Math.min(100, 
      assessment.riasec[key] + (Math.random() - 0.5) * 40
    )));
  });
  
  Object.keys(assessment.ocean).forEach(key => {
    assessment.ocean[key] = Math.round(Math.max(0, Math.min(100, 
      assessment.ocean[key] + (Math.random() - 0.5) * 40
    )));
  });
  
  Object.keys(assessment.viaIs).forEach(key => {
    assessment.viaIs[key] = Math.round(Math.max(0, Math.min(100, 
      assessment.viaIs[key] + (Math.random() - 0.5) * 40
    )));
  });
  
  return assessment;
}

/**
 * Validate data types
 */
function validateDataTypes(assessment) {
  const errors = [];
  
  // Check RIASEC
  Object.entries(assessment.riasec).forEach(([key, value]) => {
    if (!Number.isInteger(value)) {
      errors.push(`riasec.${key} is not an integer: ${value} (${typeof value})`);
    }
    if (value < 0 || value > 100) {
      errors.push(`riasec.${key} is out of range (0-100): ${value}`);
    }
  });
  
  // Check OCEAN
  Object.entries(assessment.ocean).forEach(([key, value]) => {
    if (!Number.isInteger(value)) {
      errors.push(`ocean.${key} is not an integer: ${value} (${typeof value})`);
    }
    if (value < 0 || value > 100) {
      errors.push(`ocean.${key} is out of range (0-100): ${value}`);
    }
  });
  
  // Check VIA-IS
  Object.entries(assessment.viaIs).forEach(([key, value]) => {
    if (!Number.isInteger(value)) {
      errors.push(`viaIs.${key} is not an integer: ${value} (${typeof value})`);
    }
    if (value < 0 || value > 100) {
      errors.push(`viaIs.${key} is out of range (0-100): ${value}`);
    }
  });
  
  return errors;
}

/**
 * Test assessment submission
 */
async function testAssessmentSubmission() {
  console.log('🧪 Testing Assessment Data Validation');
  console.log('====================================');
  console.log();
  
  // Test 1: Validate sample data
  console.log('📋 Test 1: Validating sample assessment data...');
  const sampleErrors = validateDataTypes(SAMPLE_ASSESSMENT);
  if (sampleErrors.length > 0) {
    console.log('❌ Sample data validation failed:');
    sampleErrors.forEach(error => console.log(`   - ${error}`));
    return false;
  }
  console.log('✅ Sample data validation passed');
  
  // Test 2: Validate random generated data
  console.log('\n📋 Test 2: Validating random generated data...');
  for (let i = 0; i < 5; i++) {
    const randomAssessment = generateRandomAssessment();
    const randomErrors = validateDataTypes(randomAssessment);
    
    if (randomErrors.length > 0) {
      console.log(`❌ Random data ${i+1} validation failed:`);
      randomErrors.forEach(error => console.log(`   - ${error}`));
      return false;
    }
    console.log(`✅ Random data ${i+1} validation passed`);
  }
  
  // Test 3: Check required fields
  console.log('\n📋 Test 3: Checking required fields...');
  const requiredRiasec = ['realistic', 'investigative', 'artistic', 'social', 'enterprising', 'conventional'];
  const requiredOcean = ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'];
  const requiredViaIs = [
    'creativity', 'curiosity', 'judgment', 'loveOfLearning', 'perspective', 'bravery',
    'perseverance', 'honesty', 'zest', 'love', 'kindness', 'socialIntelligence',
    'teamwork', 'fairness', 'leadership', 'forgiveness', 'humility', 'prudence',
    'selfRegulation', 'appreciationOfBeauty', 'gratitude', 'hope', 'humor', 'spirituality'
  ];
  
  const missingFields = [];
  
  requiredRiasec.forEach(field => {
    if (!(field in SAMPLE_ASSESSMENT.riasec)) {
      missingFields.push(`riasec.${field}`);
    }
  });
  
  requiredOcean.forEach(field => {
    if (!(field in SAMPLE_ASSESSMENT.ocean)) {
      missingFields.push(`ocean.${field}`);
    }
  });
  
  requiredViaIs.forEach(field => {
    if (!(field in SAMPLE_ASSESSMENT.viaIs)) {
      missingFields.push(`viaIs.${field}`);
    }
  });
  
  if (missingFields.length > 0) {
    console.log('❌ Missing required fields:');
    missingFields.forEach(field => console.log(`   - ${field}`));
    return false;
  }
  console.log('✅ All required fields present');
  
  // Test 4: Try actual API submission (if user is logged in)
  console.log('\n📋 Test 4: Testing API submission format...');
  console.log('ℹ️  This test requires a logged-in user token');
  console.log('ℹ️  Skipping API test - data format validation is sufficient');
  
  console.log('\n🎉 All validation tests passed!');
  console.log('✅ Assessment data format is correct for load testing');
  
  return true;
}

/**
 * Main function
 */
async function main() {
  try {
    const success = await testAssessmentSubmission();
    
    if (success) {
      console.log('\n🚀 Ready to run load test!');
      console.log('   Run: node load-test-e2e.js');
      process.exit(0);
    } else {
      console.log('\n❌ Validation failed. Fix data format before running load test.');
      process.exit(1);
    }
  } catch (error) {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  }
}

// Run validation
if (require.main === module) {
  main();
}

module.exports = {
  validateDataTypes,
  generateRandomAssessment,
  SAMPLE_ASSESSMENT
};
